#!/usr/bin/env python3
"""
Final Complete Telegram Referral Earning Bot with Full Features
Fixed async event loop issues
"""

import logging
import asyncio
import pytz
import uuid
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes

from config import Config
from src.database import Database
from src.models.user import User
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.models.referral import Referral, ReferralStatus
from src.services.user_service import UserService
from src.services.referral_service import ReferralService
from src.services.transaction_service import TransactionService
from src.services.withdrawal_service import WithdrawalService
from src.services.channel_service import ChannelService
from src.services.product_service import ProductService
from src.services.admin_settings_service import AdminSettingsService
from src.services.admin_panel_service import AdminPanelService
from src.utils.logger import setup_logger, log_admin_action
from src.utils.security import rate_limiter, ban_manager

# Setup logging
logger = setup_logger(__name__)

class FinalBotApp:
    """Final Complete Telegram Referral Bot Application"""
    
    def __init__(self):
        self.application = None
        self.database = None
        self.services = {}
        self.admin_sessions = {}
        
    async def initialize_async_components(self):
        """Initialize async components"""
        try:
            logger.info("🤖 Starting Complete Telegram Referral Earning Bot...")
            logger.info("🔄 Mode: Long Polling (no webhook/domain required)")
            
            # Validate configuration
            Config.validate_config()
            logger.info("✅ Configuration validated")
            
            # Initialize database
            self.database = Database()
            await self.database.connect()
            logger.info("✅ Database connected successfully")
            
            # Initialize services
            from src.services.task_service import TaskService

            self.services = {
                'user': UserService(self.database),
                'referral': ReferralService(self.database),
                'transaction': TransactionService(self.database),
                'withdrawal': WithdrawalService(self.database),
                'channel': ChannelService(self.database),
                'product': ProductService(self.database),
                'admin_settings': AdminSettingsService(self.database.db),
                'admin_panel': AdminPanelService(self.database.db),
                'task': TaskService(self.database.db)
            }
            logger.info("✅ Services initialized")
            
            # Initialize default products
            try:
                await self.services['product'].initialize_default_products()
                logger.info("✅ Default products initialized")
            except Exception as e:
                logger.warning(f"⚠️ Could not initialize default products: {e}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize async components: {e}")
            raise

    async def get_setting(self, setting_name: str, default_value=None):
        """Get dynamic admin setting value"""
        try:
            return await self.services['admin_settings'].get_setting_value(setting_name, default_value)
        except Exception as e:
            logger.error(f"Failed to get setting {setting_name}: {e}")
            # Fallback to Config values
            return getattr(Config, setting_name.upper(), default_value)

    def get_main_keyboard(self):
        """Get main menu keyboard with new 5-button layout"""
        keyboard = [
            [KeyboardButton("💰 Balance"), KeyboardButton("🎁 Daily Bonus")],
            [KeyboardButton("📋 Tasks"), KeyboardButton("👥 Referrals")],
            [KeyboardButton("💟 Withdraw 💟")]
        ]
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False,
            input_field_placeholder="Choose your gift adventure! 🎁"
        )
    
    def get_admin_keyboard(self):
        """Get admin menu keyboard"""
        keyboard = [
            [KeyboardButton("👥 Users"), KeyboardButton("� Withdraw 💟als")],
            [KeyboardButton("📢 Channels"), KeyboardButton("🛍️ Products")],
            [KeyboardButton("📊 Analytics"), KeyboardButton("⚙️ Bot Settings")],
            [KeyboardButton("📨 Broadcast"), KeyboardButton("🔙 Back to User")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    async def check_user_permissions(self, user_id: int) -> dict:
        """Check user permissions and restrictions"""
        result = {
            'allowed': True,
            'reason': '',
            'banned': False,
            'rate_limited': False
        }
        
        # Check if user is banned
        try:
            user = await self.services['user'].get_user(user_id)
            if user and user.is_banned:
                result['allowed'] = False
                result['banned'] = True
                result['reason'] = user.ban_reason or "You are banned from using this bot."
                return result
        except:
            pass  # Continue if user service fails
        
        # Check temporary ban
        if ban_manager.is_temp_banned(user_id):
            remaining = ban_manager.get_ban_time_remaining(user_id)
            result['allowed'] = False
            result['banned'] = True
            result['reason'] = f"You are temporarily banned. Time remaining: {remaining}"
            return result
        
        # Check rate limiting
        if rate_limiter.is_rate_limited(user_id):
            result['allowed'] = False
            result['rate_limited'] = True
            result['reason'] = "You are sending messages too quickly. Please slow down."
            return result
        
        return result
    
    def extract_referral_code(self, text: str):
        """Extract referral code from start command"""
        if not text or not text.startswith('/start'):
            return None
        
        parts = text.split()
        if len(parts) > 1:
            code = parts[1].strip()
            if len(code) == 8 and code.isalnum():
                return code.upper()
        
        return None
    
    # ==================== COMMAND HANDLERS ====================
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with forced channel subscription verification"""
        try:
            user = update.effective_user

            # Check user permissions
            permissions = await self.check_user_permissions(user.id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Check if user has already been verified in this session
            session_key = f"channel_verified_{user.id}"
            if context.user_data.get(session_key):
                # User already verified in this session, proceed normally
                await self._animated_welcome_sequence(update, context, user)
                return

            # Verify channel membership
            is_member = await self._verify_required_channels(user.id, context)

            if is_member:
                # User is member of both channels, mark as verified and proceed
                context.user_data[session_key] = True
                await self._animated_welcome_sequence(update, context, user)
            else:
                # User is not member of required channels, show join interface
                await self._show_channel_join_interface(update, context, user)

        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def _verify_required_channels(self, user_id: int, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Verify if user is member of both required channels with enhanced error handling"""
        try:
            # Get required channel IDs from Config (with corrected channel ID format)
            required_channels = Config.get_required_channels()

            logger.info(f"Starting channel verification for user {user_id}")

            for i, channel_id in enumerate(required_channels, 1):
                try:
                    logger.info(f"Checking membership in channel {i}: {channel_id}")

                    # Check membership using getChatMember
                    member = await context.bot.get_chat_member(chat_id=channel_id, user_id=user_id)

                    logger.info(f"User {user_id} status in channel {channel_id}: {member.status}")

                    # Define valid and invalid member statuses for clarity
                    valid_statuses = ['member', 'administrator', 'creator']
                    invalid_statuses = ['left', 'kicked', 'restricted']

                    # Check if user has an explicitly invalid status
                    if member.status in invalid_statuses:
                        logger.info(f"❌ User {user_id} rejected - invalid status in channel {channel_id}: {member.status}")
                        logger.info(f"   Invalid statuses: {invalid_statuses}")
                        return False

                    # Check if user has a valid status
                    if member.status in valid_statuses:
                        logger.info(f"✅ User {user_id} accepted - valid status in channel {channel_id}: {member.status}")
                    else:
                        # Handle unknown/unexpected statuses
                        logger.warning(f"⚠️ User {user_id} has unknown status in channel {channel_id}: {member.status}")
                        logger.warning(f"   Expected statuses: {valid_statuses}")
                        logger.warning(f"   Treating unknown status as invalid for security")
                        return False

                except Exception as e:
                    error_msg = str(e).lower()

                    # Specific error handling for common issues
                    if "chat not found" in error_msg:
                        logger.error(f"Channel {channel_id} not found - bot may not be admin or channel ID incorrect")
                        logger.error(f"Please verify: 1) Channel ID {channel_id} is correct, 2) Bot is admin in channel")
                    elif "user not found" in error_msg:
                        logger.error(f"User {user_id} not found in channel {channel_id}")
                    elif "forbidden" in error_msg:
                        logger.error(f"Bot lacks permission to check membership in channel {channel_id}")
                    else:
                        logger.error(f"Unexpected error checking membership for user {user_id} in channel {channel_id}: {e}")

                    # For security, treat all API errors as not joined
                    return False

            # User is member of all required channels
            logger.info(f"✅ User {user_id} verified as member of all required channels")
            return True

        except Exception as e:
            logger.error(f"Critical error in channel membership verification: {e}")
            return False

    async def _show_channel_join_interface(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user):
        """Show channel join interface with animated sequence"""
        try:
            # Extract referral code from command for later use
            referral_code = None
            if context.args:
                referral_code = self.extract_referral_code(f"/start {context.args[0]}")

            # Store referral code for later use after verification
            context.user_data['pending_referral'] = referral_code

            # Step 1: Initial loading message (same as normal flow)
            loading_msg = await update.message.reply_text(
                self._get_loading_frame_1()
            )

            await asyncio.sleep(0.15)

            # Step 2: Quick progress animation
            await loading_msg.edit_text(self._get_loading_frame(2))
            await asyncio.sleep(0.15)

            await loading_msg.edit_text(self._get_loading_frame(3))
            await asyncio.sleep(0.15)

            # Step 3: Features loading
            await loading_msg.edit_text(self._get_features_loading_frame())
            await asyncio.sleep(0.2)

            # Step 4: Channel requirement reveal
            await loading_msg.edit_text("🔐 **Checking access permissions...**")
            await asyncio.sleep(0.2)

            # Delete the loading message
            await loading_msg.delete()

            # Send join interface with join.jpg image
            join_caption = "Please join our channels to use the bot"

            # Get join links from Config
            join_links = Config.get_join_links()

            keyboard = [
                [InlineKeyboardButton("📢 JOIN CHANNEL 1", url=join_links[0])],
                [InlineKeyboardButton("📢 JOIN CHANNEL 2", url=join_links[1])],
                [InlineKeyboardButton("✅ I HAVE JOINED", callback_data="verify_channels")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                with open('join.jpg', 'rb') as photo_file:
                    await context.bot.send_photo(
                        chat_id=update.effective_chat.id,
                        photo=photo_file,
                        caption=join_caption,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
            except FileNotFoundError:
                # Fallback to text message if join.jpg not found
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=f"🔐 **Channel Verification Required**\n\n{join_caption}",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            except Exception as e:
                logger.error(f"Error sending join interface: {e}")
                # Fallback to text message on any error
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=f"🔐 **Channel Verification Required**\n\n{join_caption}",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing channel join interface: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def _animated_welcome_sequence(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user):
        """Create fast animated welcome sequence for gifts bot"""
        try:
            # Extract referral code from command
            referral_code = None
            if context.args:
                referral_code = self.extract_referral_code(f"/start {context.args[0]}")

            # Step 1: Initial loading message
            loading_msg = await update.message.reply_text(
                self._get_loading_frame_1()
            )

            await asyncio.sleep(0.15)

            # Step 2: Quick progress animation
            await loading_msg.edit_text(self._get_loading_frame(2))
            await asyncio.sleep(0.15)

            await loading_msg.edit_text(self._get_loading_frame(3))
            await asyncio.sleep(0.15)

            # Create or get user during setup
            user_data = {
                'user_id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'language_code': user.language_code,
                'is_bot': user.is_bot,
                'is_premium': getattr(user, 'is_premium', False)
            }

            try:
                db_user = await self.services['user'].create_user(user_data, referral_code)
            except Exception as e:
                logger.error(f"Failed to create user: {e}")
                db_user = None

            # Step 3: Features loading
            await loading_msg.edit_text(self._get_features_loading_frame())
            await asyncio.sleep(0.2)

            # Process referral if applicable
            if referral_code and db_user and db_user.referred_by:
                await self._process_referral(db_user.user_id, db_user.referred_by, referral_code)

            # Step 4: Success reveal
            await loading_msg.edit_text(self._get_welcome_reveal_frame())
            await asyncio.sleep(0.2)

            # Step 5: Final gifts bot welcome with image
            final_welcome = self._get_final_welcome_message(user, db_user, referral_code)

            # Delete the loading message
            await loading_msg.delete()

            # Send welcome image with caption and keyboard
            try:
                with open('start.jpg', 'rb') as photo_file:
                    await context.bot.send_photo(
                        chat_id=update.effective_chat.id,
                        photo=photo_file,
                        caption=final_welcome,
                        parse_mode='Markdown',
                        reply_markup=self.get_main_keyboard()
                    )
            except FileNotFoundError:
                # Fallback to text message if image not found
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=final_welcome,
                    parse_mode='Markdown',
                    reply_markup=self.get_main_keyboard()
                )
            except Exception as e:
                logger.error(f"Error sending welcome image: {e}")
                # Fallback to text message on any error
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=final_welcome,
                    parse_mode='Markdown',
                    reply_markup=self.get_main_keyboard()
                )

            # Mark registration as completed
            if db_user:
                await self.services['user'].mark_registration_completed(db_user.user_id)

            # Cleanup incomplete registrations (run periodically)
            await self._cleanup_incomplete_registrations()

        except Exception as e:
            logger.error(f"Error in animated welcome: {e}")
            # If welcome sequence fails, cleanup the incomplete user record
            if user:
                try:
                    await self.services['user'].cleanup_incomplete_registrations(max_age_minutes=1)
                except:
                    pass
            # Fallback to simple welcome with image
            fallback_message = "🎁 Welcome to the Gifts Bot! 🎉\n\nStart earning amazing gifts now!"
            try:
                with open('start.jpg', 'rb') as photo_file:
                    await update.message.reply_photo(
                        photo=photo_file,
                        caption=fallback_message,
                        reply_markup=self.get_main_keyboard()
                    )
            except:
                # Final fallback to text only
                await update.message.reply_text(
                    fallback_message,
                    reply_markup=self.get_main_keyboard()
                )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            user_id = update.effective_user.id
            
            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            help_text = f"""
🎁 **COMPLETE GIFTS BOT GUIDE** 🎁

┌─────────────────────────────────────┐
│           💰 EARNING SYSTEM         │
├─────────────────────────────────────┤
│ 🎁 **Daily Bonus:** 💎{Config.DAILY_BONUS_AMOUNT} every 24 hours    │
│ 👥 **Per Referral:** 💎{Config.REFERRAL_REWARD} per friend         │
│ 💸 **Min Withdrawal:** 💎{Config.MINIMUM_WITHDRAWAL}              │
└─────────────────────────────────────┘

🚀 **HOW TO EARN:**
• **Share your referral link** with friends and family
• **Claim daily bonuses** every 24 hours in IST timezone
• **Invite friends** - both you and your friend get rewards!
• **Track your progress** with real-time balance updates

🛍️ **AVAILABLE GIFTS:**
🎨 **Canva Pro** • 🎵 **Spotify Premium** • 📺 **Netflix**
🎮 **Gaming Subscriptions** • 📚 **Educational Courses**
💻 **Software Licenses** • 🛒 **Shopping Vouchers**

💎 **PREMIUM FEATURES:**
✅ **Real-time balance tracking** - See earnings instantly
✅ **Instant referral rewards** - Get paid when friends join
✅ **Professional support** - 24/7 help available
✅ **Secure transactions** - Safe and reliable payments
✅ **Multiple withdrawal options** - Choose your preferred gift
✅ **Analytics dashboard** - Track your earning progress

🎯 **QUICK START GUIDE:**
1. **Get your referral link** from the Share menu
2. **Share with friends** on social media, WhatsApp, etc.
3. **Claim daily bonus** every day for consistent earnings
4. **Reach minimum balance** of 💎{Config.MINIMUM_WITHDRAWAL} to withdraw
5. **Choose your gift** from our premium catalog

📱 **BOT COMMANDS:**
• **/start** - Begin your earning journey
• **/balance** - Check your current balance and stats
• **/help** - View this comprehensive guide
• **Menu buttons** - Access all features easily

🔗 **REFERRAL SYSTEM:**
Your unique referral code helps track your earnings. When someone joins using your link:
• You earn **💎{Config.REFERRAL_REWARD}** instantly
• They get a **welcome bonus**
• Both accounts are credited automatically

💡 **PRO TIPS:**
• Share your link in multiple places for maximum reach
• Claim daily bonus consistently for steady growth
• Engage with friends to encourage them to join
• Check your balance regularly to track progress

❓ **Need Help?**
Contact our support team anytime! We're here to help you maximize your earnings! 🌟
            """
            
            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=self.get_main_keyboard()
            )

        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def _cleanup_incomplete_registrations(self):
        """Periodically cleanup incomplete user registrations"""
        try:
            # Only run cleanup occasionally to avoid performance impact
            import random
            if random.randint(1, 10) == 1:  # 10% chance to run cleanup
                cleaned_count = await self.services['user'].cleanup_incomplete_registrations(max_age_minutes=30)
                if cleaned_count > 0:
                    logger.info(f"Cleaned up {cleaned_count} incomplete registrations")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle balance command with immediate profile display"""
        try:
            user_id = update.effective_user.id
            telegram_user = update.effective_user

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user: {e}")
                user = None

            if not user:
                profile_text = """
👤 **Profile Not Found**

❌ *Please start the bot with /start to create your profile.*
                """
            else:
                # Get today's earnings (transactions from today in IST)
                ist_tz = pytz.timezone('Asia/Kolkata')
                today_ist = datetime.now(ist_tz).date()
                today_start_ist = ist_tz.localize(datetime.combine(today_ist, datetime.min.time()))
                today_start_utc = today_start_ist.astimezone(timezone.utc)

                try:
                    # Get today's transactions
                    today_transactions = await self.services['transaction'].get_user_transactions_since(
                        user_id, today_start_utc
                    )
                    today_earnings = sum(t.amount for t in today_transactions if t.amount > 0)
                except:
                    today_earnings = 0.0

                # Format join date with proper datetime handling
                try:
                    if user.created_at and isinstance(user.created_at, datetime):
                        join_date = user.created_at.strftime("%d %b %Y")
                    else:
                        join_date = "Unknown"
                except (AttributeError, ValueError):
                    join_date = "Unknown"

                # Account status
                status_icon = "🎉" if user.balance >= Config.MINIMUM_WITHDRAWAL else "📈"
                status_text = "Ready for withdrawal!" if user.balance >= Config.MINIMUM_WITHDRAWAL else "Keep earning!"

                profile_text = f"""
╭─────────────────────────╮
│  ✦ **{user.get_display_name()}'s Profile** ✦  │
╰─────────────────────────╯

◆ *💰 WALLET OVERVIEW* ◆

▫ **Balance:** **💎{user.balance:.2f}** {status_icon}
▫ **Today:** **💎{today_earnings:.2f}**

◇ *👥 REFERRAL STATS* ◇\n
▪ **Referrals:** **{user.successful_referrals}** people
▪ **Earned:** *💎{user.successful_referrals * Config.REFERRAL_REWARD:.2f} from referrals*
▪ **Your Code:** `{user.referral_code}`

═══════════════════════

✧ *Member since {join_date}* ✧
**{status_text}**
                """

            await update.message.reply_text(
                profile_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await update.message.reply_text("❌ Failed to load profile information.")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        try:
            user_id = update.effective_user.id
            message_text = update.message.text

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Handle main menu buttons
            if message_text == "💰 Balance":
                await self.balance_command(update, context)
            elif message_text == "🎁 Daily Bonus":
                await self._handle_daily_bonus(update, context)
            elif message_text == "📋 Tasks":
                await self._handle_tasks_menu(update, context)
            elif message_text == "👥 Referrals":
                await self._handle_referrals_menu(update, context)
            elif message_text == "💟 Withdraw 💟":
                await self._handle_withdraw_menu(update, context)
            elif message_text == "💟 Withdrawals 💟":
                # Admin withdrawals management
                if user_id in Config.ADMIN_USER_IDS:
                    await self._handle_admin_withdrawals_menu(update, context)
                else:
                    await self._handle_withdraw_menu(update, context)
            elif message_text == "📊 Statistics":
                await self._handle_stats_menu(update, context)
            elif message_text == "⚙️ Settings":
                await self._handle_settings_menu(update, context)
            elif message_text == "❓ Help & Support":
                await self.help_command(update, context)
            else:
                # Check if admin is changing a setting, creating a task, or creating a broadcast
                if 'admin_setting_change' in context.user_data:
                    await self._process_admin_setting_change(update, context, message_text)
                elif 'admin_task_creation' in context.user_data:
                    await self._process_task_creation(update, context, message_text)
                elif 'field_edit' in context.user_data:
                    await self._process_field_edit(update, context, message_text)
                elif 'admin_broadcast_creation' in context.user_data:
                    await self._process_broadcast_creation(update, context, message_text)
                else:
                    await update.message.reply_text(
                        "🎁 Use the gift buttons below to start earning! ✨",
                        reply_markup=self.get_main_keyboard()
                    )

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def handle_photo(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle photo uploads for task creation and submissions"""
        try:
            user_id = update.effective_user.id

            # Check if admin is creating a task with reference image
            if 'admin_task_creation' in context.user_data:
                task_data = context.user_data['admin_task_creation']

                if task_data.get('step') == 'reference_image':
                    # Get the largest photo size
                    photo = update.message.photo[-1]
                    file = await context.bot.get_file(photo.file_id)

                    # Store the file_id as reference image URL (in production, you'd upload to cloud storage)
                    task_data['reference_image_url'] = photo.file_id
                    task_data['step'] = 'image_caption'
                    context.user_data['admin_task_creation'] = task_data

                    await update.message.reply_text(
                        "✅ Reference image uploaded!\n\nNow send detailed instructions for users (what they should do/screenshot):",
                        parse_mode='Markdown'
                    )
                    return

            # Check if user is submitting task image
            elif 'user_task_submission' in context.user_data:
                submission_data = context.user_data['user_task_submission']
                task_id = submission_data['task_id']

                # Get the largest photo size
                photo = update.message.photo[-1]

                # Validate submission
                validation = await self._validate_task_submission(user_id, task_id, {'file_id': photo.file_id})

                if not validation['valid']:
                    error_msg = "❌ **Submission Invalid**\n\n" + "\n".join(validation['errors'])
                    await update.message.reply_text(error_msg, parse_mode='Markdown')
                    # Clear submission state
                    del context.user_data['user_task_submission']
                    return

                # Log submission attempt
                await self._log_task_action(user_id, "TASK_SUBMISSION_ATTEMPT", task_id, f"Image: {photo.file_id}")

                # Submit for review
                success = await self.services['task'].submit_task_for_review(
                    user_id=user_id,
                    task_id=task_id,
                    image_url=photo.file_id,
                    notes=submission_data.get('notes', '')
                )

                # Clear submission state
                del context.user_data['user_task_submission']

                if success:
                    await self._log_task_action(user_id, "TASK_SUBMITTED", task_id, "Submitted for admin review")
                    await update.message.reply_text(
                        "✅ **Task submitted for review!**\n\nYour submission has been sent to admins for approval. You'll be notified once it's reviewed.\n\n⚠️ **Important:** Do not submit the same task again while it's under review.",
                        parse_mode='Markdown'
                    )
                else:
                    await update.message.reply_text("❌ Failed to submit task. Please try again.")

                return

            # Default response for unexpected photos
            await update.message.reply_text("📸 Photo received, but no active task requires an image.")

        except Exception as e:
            logger.error(f"Error handling photo: {e}")
            await update.message.reply_text("❌ Failed to process photo.")

    async def _handle_daily_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle daily bonus claiming with enhanced 2-3 second animation"""
        try:
            user_id = update.effective_user.id

            # Enhanced checking animation sequence
            checking_msg = await update.message.reply_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🔍 Scanning for gifts...\n░░░░░ 0%"
            )

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🔍 Scanning for gifts...\n▓░░░░ 20%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🎯 Gift detected!\n▓▓░░░ 40%",
                parse_mode='Markdown'
            )

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for daily bonus: {e}")
                user = None

            if not user:
                await checking_msg.edit_text("❌ User not found. Please start the bot with /start")
                return

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n✨ Verifying eligibility...\n▓▓▓░░ 60%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🎉 Verification complete!\n▓▓▓▓░ 80%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🚀 Ready to claim!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.2)

            if not user.can_claim_daily_bonus():
                # Get next claim time in IST
                next_claim_ist = user.get_next_daily_bonus_time_ist()

                if next_claim_ist:
                    # Calculate time remaining until next 12:00 AM IST
                    ist_tz = pytz.timezone('Asia/Kolkata')
                    now_ist = datetime.now(ist_tz)
                    remaining = next_claim_ist - now_ist

                    hours = int(remaining.total_seconds() // 3600)
                    minutes = int((remaining.total_seconds() % 3600) // 60)

                    cooldown_text = f"""
✨ **DAILY GIFT** ✨

🎁 **You already claimed today's bonus!**

⏰ **Next gift at:** 12:00 AM IST
🕐 **Time remaining:** {hours}h {minutes}m
💎 **Daily gift:** 💎{Config.DAILY_BONUS_AMOUNT}

🌟 Come back tomorrow for your next gift!
                    """
                else:
                    cooldown_text = """
✨ **DAILY GIFT** ✨

🎁 **Gift not ready yet!**

Come back later for your daily gift! 🌟
                    """

                await checking_msg.edit_text(cooldown_text, parse_mode='Markdown')
                return

            # Enhanced claiming animation sequence
            await checking_msg.edit_text(
                "🎁 **GIFT CLAIMING INITIATED** 🎁\n\n🔄 Processing your gift...\n💫 ✨ 💫",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.4)
            await checking_msg.edit_text(
                "🎁 **GIFT CLAIMING INITIATED** 🎁\n\n💰 Adding to your wallet...\n🌟 ✨ 🌟 ✨ 🌟",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.4)
            await checking_msg.edit_text(
                "🎁 **GIFT CLAIMING INITIATED** 🎁\n\n🎉 Almost there...\n🎊 🎉 🎊 🎉 🎊 🎉",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.3)

            # Get dynamic daily bonus amount
            daily_bonus_amount = await self.get_setting('daily_bonus_amount', Config.DAILY_BONUS_AMOUNT)

            # Claim daily bonus
            user.claim_daily_bonus(daily_bonus_amount)
            await self.services['user'].update_user(user)

            # Create transaction record
            try:
                await self.services['transaction'].create_transaction(
                    user_id=user_id,
                    amount=daily_bonus_amount,
                    transaction_type=TransactionType.DAILY_BONUS,
                    description="Daily bonus claimed"
                )
            except Exception as e:
                logger.error(f"Failed to create transaction: {e}")

            # Get dynamic referral reward for display
            referral_reward = await self.get_setting('referral_reward', Config.REFERRAL_REWARD)

            # Concise success message
            bonus_text = f"""
🎊 **Daily Gift Claimed!** 🎊

💰 **+💎{daily_bonus_amount}** added to your balance
💎 **New Balance:** **💎{user.balance:.2f}**

🕐 *Next gift available tomorrow at 12:00 AM IST*
            """

            await checking_msg.edit_text(
                bonus_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in daily bonus: {e}")
            await update.message.reply_text("❌ Failed to claim daily bonus.")

    async def _handle_referrals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle referrals menu with immediate display"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referrals: {e}")
                user = None

            if not user:
                # Fallback referral link
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
                referral_count = 0
                referral_earnings = 0.0
                total_referrals = 0
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
                try:
                    referral_count = await self.services['referral'].get_successful_referral_count(user_id)
                    referral_earnings = await self.services['referral'].get_referral_earnings(user_id)
                    total_referrals = user.total_referrals
                except Exception as e:
                    logger.error(f"Failed to get referral stats: {e}")
                    referral_count = 0
                    referral_earnings = 0.0
                    total_referrals = 0

            # Calculate potential earnings
            potential_earnings = referral_count * Config.REFERRAL_REWARD
            success_rate = (referral_count / total_referrals * 100) if total_referrals > 0 else 0

            referral_text = f"""
👥 **Your Referral Link** 👥

🔗 `{referral_link}`

📊 **Stats:**
• **Successful Referrals:** **{referral_count}** (*💎{referral_earnings:.2f} earned*)
• **Reward per Friend:** **💎{Config.REFERRAL_REWARD}**

*Share your link and earn 💎{Config.REFERRAL_REWARD} per friend!*
            """

            # Create single SHARE NOW button with Telegram share URL
            share_url = f"https://t.me/share/url?text=https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}%0AI've%20Got%20Up%20To%20💎100!%20Click%20URL%20To%20Join%20&%20Make%20Money%20Now!"

            keyboard = [
                [InlineKeyboardButton("🚀 SHARE NOW", url=share_url)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )except Exception as e:
            logger.error(f"Error in referrals menu: {e}")
            await update.message.reply_text("❌ Failed to get referral information.")

    async def _handle_tasks_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle tasks menu with dynamic admin-created tasks"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for tasks: {e}")
                user = None

            if not user:
                await update.message.reply_text("❌ User not found. Please start the bot with /start")
                return

            # Get user tasks with their completion status
            user_tasks = await self.services['task'].get_user_tasks(user_id)

            if not user_tasks:
                await update.message.reply_text(
                    "📋 **No Tasks Available**\n\nNo tasks have been created yet. Check back later!",
                    parse_mode='Markdown'
                )
                return

            # Calculate statistics
            total_tasks = len(user_tasks)
            completed_tasks = sum(1 for _, user_task in user_tasks if user_task.status.value == 'completed')
            pending_tasks = sum(1 for _, user_task in user_tasks if user_task.status.value == 'pending_review')
            available_rewards = sum(task.reward_amount for task, user_task in user_tasks
                                  if user_task.status.value not in ['completed', 'pending_review'])

            # Create tasks display
            tasks_text = f"""📋 **Available Tasks**

**Statistics:**
• Total Tasks: {total_tasks}
• Completed: {completed_tasks}
• Pending Review: {pending_tasks}
• Available Rewards: 💎{available_rewards:.0f}

**Tasks:**
"""

            # Add each task to the display
            keyboard = []
            for task, user_task in user_tasks:
                status_info = self._get_task_status_info(user_task.status.value)

                tasks_text += f"{status_info['icon']} **{task.task_name}** - 💎{task.reward_amount}\n"
                tasks_text += f"   *{task.description or 'Complete this task to earn rewards'}*\n"
                tasks_text += f"   Status: {status_info['text']}\n\n"

                # Add button if task is available
                if user_task.status.value in ['not_started', 'in_progress', 'rejected']:
                    button_text = f"🎯 {task.task_name} (+💎{task.reward_amount})"
                    callback_data = f"task_{task.task_id}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])

            # Add refresh button
            keyboard.append([InlineKeyboardButton("🔄 Refresh Tasks", callback_data="refresh_tasks")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                tasks_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )except Exception as e:
            logger.error(f"Error in tasks menu: {e}")
            await update.message.reply_text("❌ Failed to load tasks.")

    def _get_task_status_info(self, status: str) -> dict:
        """Get status icon and text for task display"""
        status_map = {
            'not_started': {'icon': '⏳', 'text': 'Available'},
            'in_progress': {'icon': '🔄', 'text': 'In Progress'},
            'pending_review': {'icon': '⏰', 'text': 'Under Review'},
            'completed': {'icon': '✅', 'text': 'Completed'},
            'rejected': {'icon': '❌', 'text': 'Rejected - Try Again'}
        }
        return status_map.get(status, {'icon': '❓', 'text': 'Unknown'})

    async def _refresh_tasks_callback(self, query, context):
        """Refresh tasks display via callback"""
        try:
            user_id = query.from_user.id

            # Get user tasks with their completion status
            user_tasks = await self.services['task'].get_user_tasks(user_id)

            if not user_tasks:
                await query.edit_message_text(
                    "📋 **No Tasks Available**\n\nNo tasks have been created yet. Check back later!",
                    parse_mode='Markdown'
                )
                return

            # Calculate statistics
            total_tasks = len(user_tasks)
            completed_tasks = sum(1 for _, user_task in user_tasks if user_task.status.value == 'completed')
            pending_tasks = sum(1 for _, user_task in user_tasks if user_task.status.value == 'pending_review')
            available_rewards = sum(task.reward_amount for task, user_task in user_tasks
                                  if user_task.status.value not in ['completed', 'pending_review'])

            # Create tasks display
            tasks_text = f"""📋 **Available Tasks** 🔄

**Statistics:**
• Total Tasks: {total_tasks}
• Completed: {completed_tasks}
• Pending Review: {pending_tasks}
• Available Rewards: 💎{available_rewards:.0f}

**Tasks:**
"""

            # Add each task to the display
            keyboard = []
            for task, user_task in user_tasks:
                status_info = self._get_task_status_info(user_task.status.value)

                tasks_text += f"{status_info['icon']} **{task.task_name}** - 💎{task.reward_amount}\n"
                tasks_text += f"   *{task.description or 'Complete this task to earn rewards'}*\n"
                tasks_text += f"   Status: {status_info['text']}\n\n"

                # Add button if task is available
                if user_task.status.value in ['not_started', 'in_progress', 'rejected']:
                    button_text = f"🎯 {task.task_name} (+💎{task.reward_amount})"
                    callback_data = f"task_{task.task_id}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])

            # Add refresh button
            keyboard.append([InlineKeyboardButton("🔄 Refresh Tasks", callback_data="refresh_tasks")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                tasks_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error refreshing tasks: {e}")
            await query.answer("❌ Failed to refresh tasks.")

    async def admin_settings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Admin command to view and manage bot settings"""
        try:
            user_id = update.effective_user.id

            # Check if user is admin
            if user_id not in Config.ADMIN_USER_IDS:
                await update.message.reply_text("❌ Access denied. Admin only command.")
                return

            # Get current settings
            settings = await self.services['admin_settings'].get_settings()

            settings_text = f"""
🔧 **ADMIN SETTINGS PANEL** 🔧

┌─────────────────────────────────────┐
│              💰 FINANCIAL SETTINGS  │
├─────────────────────────────────────┤
│ 💰 **Referral Reward:** 💎{settings.referral_reward}
│ 🎁 **Daily Bonus:** 💎{settings.daily_bonus_amount}
│ 💸 **Min Withdrawal:** 💎{settings.minimum_withdrawal}
│ ⏰ **Withdrawal Cooldown:** {settings.withdrawal_cooldown_hours}h
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              🎯 TASK REWARDS        │
├─────────────────────────────────────┤
│ 📺 **Join Channel:** 💎{settings.task_rewards.get('join_channel', 25)}
│ 📤 **Share Bot:** 💎{settings.task_rewards.get('share_bot', 50)}
│ 🔥 **Daily Streak:** 💎{settings.task_rewards.get('daily_streak', 100)}
│ 👥 **Referral Milestone:** 💎{settings.task_rewards.get('referral_milestone', 150)}
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              ⚙️ BOT SETTINGS        │
├─────────────────────────────────────┤
│ 🎉 **Welcome Bonus:** 💎{settings.welcome_bonus}
│ 📅 **Max Daily Claims:** {settings.max_daily_claims}
│ 👥 **Referral Limit/Day:** {settings.referral_limit_per_day}
└─────────────────────────────────────┘

📝 **Last Updated:** {settings.updated_at.strftime('%d %b %Y %H:%M') if settings.updated_at else 'Never'}
👤 **Updated By:** Admin ID {settings.updated_by if settings.updated_by else 'System'}

🔧 **Commands:**
• `/set_referral_reward <amount>` - Set referral reward
• `/set_daily_bonus <amount>` - Set daily bonus
• `/set_min_withdrawal <amount>` - Set minimum withdrawal
• `/reset_settings` - Reset to defaults
            """

            await update.message.reply_text(settings_text, parse_mode='Markdown')except Exception as e:
            logger.error(f"Error in admin settings: {e}")
            await update.message.reply_text("❌ Failed to load admin settings.")

    async def set_referral_reward_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Admin command to set referral reward amount"""
        try:
            user_id = update.effective_user.id

            if user_id not in Config.ADMIN_USER_IDS:
                await update.message.reply_text("❌ Access denied. Admin only command.")
                return

            if not context.args or len(context.args) != 1:
                await update.message.reply_text("❌ Usage: /set_referral_reward <amount>\nExample: /set_referral_reward 15")
                return

            try:
                amount = float(context.args[0])
                if amount < 0:
                    await update.message.reply_text("❌ Amount must be positive.")
                    return

                success = await self.services['admin_settings'].update_settings(
                    admin_id=user_id,
                    referral_reward=amount
                )

                if success:
                    await update.message.reply_text(f"✅ Referral reward updated to 💎{amount}")
                    log_admin_action(user_id, "REFERRAL_REWARD_UPDATED", f"New amount: {amount}")
                else:
                    await update.message.reply_text("❌ Failed to update setting.")

            except ValueError:
                await update.message.reply_text("❌ Invalid amount. Please enter a valid number.")

        except Exception as e:
            logger.error(f"Error setting referral reward: {e}")
            await update.message.reply_text("❌ Failed to update referral reward.")

    async def _handle_withdraw_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdraw menu with 4-5 second cool animation"""
        try:
            user_id = update.effective_user.id

            # Start the epic 4-5 second withdrawal animation
            withdraw_msg = await update.message.reply_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n🔄 Starting secure connection...\n░░░░░ 0%"
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n🔐 Establishing secure tunnel...\n▓░░░░ 20%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n🛡️ Verifying security protocols...\n▓▓░░░ 40%",
                parse_mode='Markdown'
            )

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for withdrawal: {e}")
                user = None

            if not user:
                await withdraw_msg.edit_text("❌ User not found. Please start the bot with /start")
                return

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n💰 Accessing your balance...\n▓▓▓░░ 60%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n🎯 Calculating withdrawal options...\n▓▓▓▓░ 80%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n✅ System ready!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM READY** 💸\n\n🚀 Preparing your options...\n💎 ✨ 💎 ✨ 💎",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)

            # Get dynamic minimum withdrawal amount
            minimum_withdrawal = await self.get_setting('minimum_withdrawal', Config.MINIMUM_WITHDRAWAL)
            referral_reward = await self.get_setting('referral_reward', Config.REFERRAL_REWARD)
            daily_bonus_amount = await self.get_setting('daily_bonus_amount', Config.DAILY_BONUS_AMOUNT)

            # Check balance against minimum withdrawal limit
            can_withdraw = user.balance >= minimum_withdrawal
            needed_amount = minimum_withdrawal - user.balance if not can_withdraw else 0

            if can_withdraw:
                withdraw_text = f"""
💸 **Withdrawal Available** 💸

💰 **Balance:** **💎{user.balance:.2f}**
✅ **Status:** Ready for withdrawal!
🎯 **Minimum:** 💎{minimum_withdrawal}

*Contact admin to process your withdrawal.*
                """

                # Simple keyboard with only implemented features
                keyboard = [
                    [InlineKeyboardButton("📞 Contact Admin", callback_data="contact_admin")]
                ]
            else:
                withdraw_text = f"""
💟 **Withdrawal Center** 💟

💰 **Balance:** **💎{user.balance:.2f}**
🎯 **Minimum:** **💎{minimum_withdrawal}**\n
📈 **Need:** **💎{needed_amount:.2f}** more

*Please complete the minimum amount criteria to unlock withdrawals!*

                """

                # Simple keyboard for earning options
                keyboard = [
                    [InlineKeyboardButton("👥 Invite Friends", callback_data="referral_menu")],
                    [InlineKeyboardButton("🎁 Daily Bonus", callback_data="daily_bonus")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await withdraw_msg.edit_text(
                withdraw_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )except Exception as e:
            logger.error(f"Error in withdraw menu: {e}")
            await update.message.reply_text("❌ Failed to access withdrawal options.")

    async def _handle_admin_withdrawals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle admin withdrawals management menu"""
        try:
            # Get withdrawal statistics
            pending_withdrawals = await self.database.withdrawals.count_documents({'status': 'pending'})
            completed_withdrawals = await self.database.withdrawals.count_documents({'status': 'completed'})
            total_withdrawn = await self.database.withdrawals.aggregate([
                {'$match': {'status': 'completed'}},
                {'$group': {'_id': None, 'total': {'$sum': '$amount'}}}
            ]).to_list(1)

            total_amount = total_withdrawn[0]['total'] if total_withdrawn else 0

            admin_text = f"""
💟 **ADMIN WITHDRAWALS MANAGEMENT** 💟

📊 **Statistics:**
• **Pending Requests:** {pending_withdrawals}
• **Completed:** {completed_withdrawals}
• **Total Withdrawn:** 💎{total_amount:.2f}

**Management Options:**
            """

            keyboard = [
                [InlineKeyboardButton("📋 Pending Requests", callback_data="admin_withdrawals_pending")],
                [InlineKeyboardButton("✅ Completed Withdrawals", callback_data="admin_withdrawals_completed")],
                [InlineKeyboardButton("📊 Withdrawal Statistics", callback_data="admin_withdrawals_stats")],
                [InlineKeyboardButton("⚙️ Withdrawal Settings", callback_data="admin_settings_menu")],
                [InlineKeyboardButton("🔙 Back to Admin Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                admin_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in admin withdrawals menu: {e}")
            await update.message.reply_text("❌ Failed to access admin withdrawals management.")

    async def _show_admin_withdrawals_menu_callback(self, query, context):
        """Show admin withdrawals management menu for callback queries"""
        try:
            # Get withdrawal statistics
            pending_withdrawals = await self.database.withdrawals.count_documents({'status': 'pending'})
            completed_withdrawals = await self.database.withdrawals.count_documents({'status': 'completed'})
            total_withdrawn = await self.database.withdrawals.aggregate([
                {'$match': {'status': 'completed'}},
                {'$group': {'_id': None, 'total': {'$sum': '$amount'}}}
            ]).to_list(1)

            total_amount = total_withdrawn[0]['total'] if total_withdrawn else 0

            admin_text = f"""
💟 **ADMIN WITHDRAWALS MANAGEMENT** 💟

📊 **Statistics:**
• **Pending Requests:** {pending_withdrawals}
• **Completed:** {completed_withdrawals}
• **Total Withdrawn:** 💎{total_amount:.2f}

**Management Options:**
            """

            keyboard = [
                [InlineKeyboardButton("📋 Pending Requests", callback_data="admin_withdrawals_pending")],
                [InlineKeyboardButton("✅ Completed Withdrawals", callback_data="admin_withdrawals_completed")],
                [InlineKeyboardButton("📊 Withdrawal Statistics", callback_data="admin_withdrawals_stats")],
                [InlineKeyboardButton("⚙️ Withdrawal Settings", callback_data="admin_settings_menu")],
                [InlineKeyboardButton("🔙 Back to Admin Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                admin_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in admin withdrawals menu callback: {e}")
            await query.answer("❌ Failed to access admin withdrawals management.")

    async def _get_withdrawal_statistics(self):
        """Get comprehensive withdrawal statistics"""
        try:
            # Basic counts
            total_requests = await self.database.withdrawals.count_documents({})
            pending_count = await self.database.withdrawals.count_documents({'status': 'pending'})
            completed_count = await self.database.withdrawals.count_documents({'status': 'completed'})
            rejected_count = await self.database.withdrawals.count_documents({'status': 'rejected'})

            # Financial data
            total_withdrawn_agg = await self.database.withdrawals.aggregate([
                {'$match': {'status': 'completed'}},
                {'$group': {'_id': None, 'total': {'$sum': '$amount'}}}
            ]).to_list(1)
            total_withdrawn = total_withdrawn_agg[0]['total'] if total_withdrawn_agg else 0

            pending_amount_agg = await self.database.withdrawals.aggregate([
                {'$match': {'status': 'pending'}},
                {'$group': {'_id': None, 'total': {'$sum': '$amount'}}}
            ]).to_list(1)
            pending_amount = pending_amount_agg[0]['total'] if pending_amount_agg else 0

            # Calculate averages
            average_withdrawal = total_withdrawn / completed_count if completed_count > 0 else 0
            completion_rate = (completed_count / total_requests * 100) if total_requests > 0 else 0

            return {
                'total_requests': total_requests,
                'pending_count': pending_count,
                'completed_count': completed_count,
                'rejected_count': rejected_count,
                'total_withdrawn': total_withdrawn,
                'pending_amount': pending_amount,
                'average_withdrawal': average_withdrawal,
                'completion_rate': completion_rate,
                'avg_processing_time': 24  # Placeholder - would calculate from actual data
            }

        except Exception as e:
            logger.error(f"Error getting withdrawal statistics: {e}")
            return {
                'total_requests': 0,
                'pending_count': 0,
                'completed_count': 0,
                'rejected_count': 0,
                'total_withdrawn': 0,
                'pending_amount': 0,
                'average_withdrawal': 0,
                'completion_rate': 0,
                'avg_processing_time': 0
            }

    async def _handle_stats_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle stats menu"""
        try:
            user_id = update.effective_user.id

            try:
                stats = await self.services['user'].get_user_statistics(user_id)
            except Exception as e:
                logger.error(f"Failed to get user stats: {e}")
                stats = None

            if not stats:
                stats_text = """
📊 **Your Statistics**

👤 **Profile:**
• User ID: `{user_id}`
• Joined: Recently

💰 **Earnings:**
• Current Balance: 💎0.00
• Total Earned: 💎0.00

👥 **Referrals:**
• Successful Referrals: 0

📈 **Activity:**
• Account Status: ✅ Active
                """.format(user_id=user_id)
            else:
                user_data = stats['user']
                join_date = datetime.fromisoformat(user_data['created_at'].replace('Z', '+00:00')).strftime('%d/%m/%Y')

                stats_text = f"""
📊 **Your Statistics**

👤 **Profile:**
• Name: {user_data.get('first_name', 'N/A')}
• Username: @{user_data.get('username', 'N/A')}
• User ID: `{user_data['user_id']}`
• Joined: {join_date}

💰 **Earnings:**
• Current Balance: 💎{user_data['balance']:.2f}
• Total Withdrawals: 💎{user_data.get('total_withdrawals', 0):.2f}

👥 **Referrals:**
• Successful Referrals: {stats['referral_count']}
• Referral Earnings: 💎{stats['referral_count'] * Config.REFERRAL_REWARD:.2f}

📈 **Activity:**
• Total Transactions: {stats['transaction_count']}
• Account Status: {'✅ Active' if user_data['is_active'] else '❌ Inactive'}
                """

            await update.message.reply_text(
                stats_text,
                parse_mode='Markdown'
            )except Exception as e:
            logger.error(f"Error in stats menu: {e}")
            await update.message.reply_text("❌ Failed to get statistics.")

    async def _handle_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle settings menu"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for settings: {e}")
                user = None

            if not user:
                settings_text = f"""
⚙️ **Account Settings**

**Profile Information:**
• User ID: `{user_id}`
• Status: ✅ Active

**Referral Code:** `REF{user_id}`

Use the buttons below to manage your settings:
                """
            else:
                settings_text = f"""
⚙️ **Account Settings**

**Profile Information:**
• Name: {user.get_display_name()}
• Username: @{user.username or 'N/A'}
• User ID: `{user.user_id}`
• Language: {user.language_code or 'N/A'}

**Account Status:**
• Status: {'✅ Active' if user.is_active else '❌ Inactive'}
• Premium: {'✅ Yes' if user.is_premium else '❌ No'}
• Joined: {user.created_at.strftime('%d/%m/%Y')}

**Referral Code:** `{user.referral_code}`

Use the buttons below to manage your settings:
                """

            # Create inline keyboard for settings
            keyboard = [
                [InlineKeyboardButton("🔔 Notifications", callback_data="settings_notifications")],
                [InlineKeyboardButton("🌐 Language", callback_data="settings_language")],
                [InlineKeyboardButton("📱 Account Info", callback_data="settings_account")],
                [InlineKeyboardButton("❓ Help", callback_data="settings_help")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )except Exception as e:
            logger.error(f"Error in settings menu: {e}")
            await update.message.reply_text("❌ Failed to access settings.")

    # ==================== CALLBACK HANDLERS ====================

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries from inline keyboards"""
        try:
            query = update.callback_query
            user_id = query.from_user.id
            data = query.data

            await query.answer()

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await query.edit_message_text(permissions['reason'])
                return

            # Route callback based on data
            if data.startswith("admin_"):
                # Check admin access for admin callbacks
                if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                    await query.edit_message_text("❌ Access denied. Admin privileges required.")
                    return
                await self._handle_admin_callbacks(query, context, data)
            elif data.startswith("balance_"):
                await self._handle_balance_callbacks(query, context, data)
            elif data.startswith("copy_referral_link"):
                await self._copy_referral_link(query, context)
            elif data.startswith("withdraw_"):
                await self._handle_withdrawal_callbacks(query, context, data)
            elif data.startswith("admin_withdrawals_"):
                await self._handle_admin_withdrawals_callbacks(query, context, data)
            elif data.startswith("settings_"):
                await self._handle_settings_callbacks(query, context, data)
            elif data.startswith("task_join_"):
                channel_task_id = data.replace("task_join_", "")
                await self._handle_join_channel_task(query, context, channel_task_id)
            elif data.startswith("task_verify_"):
                channel_task_id = data.replace("task_verify_", "")
                await self._verify_channel_membership(query, context, channel_task_id)
            elif data.startswith("task_submit_"):
                image_task_id = data.replace("task_submit_", "")
                await self._start_image_submission(query, context, image_task_id)
            elif data == "refresh_tasks":
                await self._refresh_tasks_callback(query, context)
            elif data.startswith("task_type_"):
                # Handle task type selection for admin task creation
                if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                    await query.edit_message_text("❌ Access denied. Admin privileges required.")
                    return
                await self._handle_task_type_selection(query, context, data.replace("task_type_", ""))
            elif data == "task_create_confirm":
                # Handle task creation confirmation
                if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                    await query.edit_message_text("❌ Access denied. Admin privileges required.")
                    return
                await self._create_task_from_data(query, context)
            elif data == "task_verification_manual":
                # Handle manual verification mode selection
                if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                    await query.edit_message_text("❌ Access denied. Admin privileges required.")
                    return
                await self._set_task_verification_mode(query, context, "manual_review")
            elif data == "task_verification_auto":
                # Handle auto verification mode selection
                if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                    await query.edit_message_text("❌ Access denied. Admin privileges required.")
                    return
                await self._set_task_verification_mode(query, context, "auto_approve")
            elif data.startswith("task_"):
                task_id = data.replace("task_", "")
                await self._handle_task_action(query, context, task_id)
            elif data == "referral_menu":
                await self._handle_referral_menu_callback(query, context)
            elif data == "daily_bonus":
                await self._handle_daily_bonus_callback(query, context)
            elif data == "contact_admin":
                await self._handle_contact_admin_callback(query, context)
            elif data == "withdraw_menu":
                await self._handle_withdraw_menu_callback(query, context)
            elif data == "verify_channels":
                await self._handle_channel_verification_callback(query, context)
            else:
                await query.edit_message_text("🚧 Feature coming soon...")

        except Exception as e:
            logger.error(f"Error handling callback: {e}")
            try:
                await query.answer("❌ An error occurred.")
            except:
                pass

    async def _handle_admin_callbacks(self, query, context, data):
        """Handle admin panel callbacks"""
        try:
            user_id = query.from_user.id

            # Main menu navigation
            if data == "admin_main":
                await self._show_admin_main_menu_callback(query, context)
            elif data == "admin_users":
                await self._show_user_management_menu_callback(query, context)
            elif data == "admin_settings_menu":
                await self._show_bot_settings_menu_callback(query, context)
            elif data == "admin_tasks":
                await self._show_task_management_menu(query, context)
            elif data == "admin_broadcast":
                await self._show_broadcast_menu(query, context)
            elif data == "admin_analytics":
                await self._show_analytics_menu(query, context)
            elif data == "admin_health":
                await self._show_system_health(query, context)
            elif data == "admin_logs":
                await self._show_admin_logs(query, context)

            # User management callbacks
            elif data == "admin_users_list":
                await self._show_users_list(query, context, 1)
            elif data.startswith("admin_users_list_"):
                page = int(data.split("_")[-1])
                await self._show_users_list(query, context, page)
            elif data == "admin_users_search":
                await query.edit_message_text(
                    "🔍 **USER SEARCH**\n\nPlease send the user ID or username to search for:",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_users")]])
                )
            elif data == "admin_users_edit":
                await query.edit_message_text(
                    "✏️ **EDIT USER**\n\nPlease send the user ID to edit:",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_users")]])
                )
            elif data == "admin_users_ban":
                await query.edit_message_text(
                    "🚫 **BAN/UNBAN USER**\n\nPlease send the user ID to ban/unban:",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_users")]])
                )
            elif data == "admin_users_delete":
                await query.edit_message_text(
                    "🗑️ **DELETE USER**\n\n⚠️ **WARNING:** This action is irreversible!\n\nPlease send the user ID to delete:",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_users")]])
                )
            elif data == "admin_users_reports":
                await self._show_user_reports(query, context)

            # Bot settings callbacks
            elif data == "admin_set_referral":
                await query.edit_message_text(
                    "💰 **SET REFERRAL REWARD**\n\nCurrent: 💎{}\n\nEnter new referral reward amount (💎1-💎1000):".format(
                        (await self.services['admin_settings'].get_settings()).referral_reward
                    ),
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_settings_menu")]])
                )
            elif data == "admin_set_daily":
                await query.edit_message_text(
                    "🎁 **SET DAILY BONUS**\n\nCurrent: 💎{}\n\nEnter new daily bonus amount (💎1-💎100):".format(
                        (await self.services['admin_settings'].get_settings()).daily_bonus_amount
                    ),
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_settings_menu")]])
                )
            elif data == "admin_set_withdrawal":
                await query.edit_message_text(
                    "💸 **SET MINIMUM WITHDRAWAL**\n\nCurrent: 💎{}\n\nEnter new minimum withdrawal amount (💎100-💎10000):".format(
                        (await self.services['admin_settings'].get_settings()).minimum_withdrawal
                    ),
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_settings_menu")]])
                )
            elif data == "admin_set_cooldown":
                await query.edit_message_text(
                    "⏰ **SET WITHDRAWAL COOLDOWN**\n\nCurrent: {} hours\n\nEnter new cooldown period (1-168 hours):".format(
                        (await self.services['admin_settings'].get_settings()).withdrawal_cooldown_hours
                    ),
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_settings_menu")]])
                )
            elif data == "admin_set_welcome":
                await query.edit_message_text(
                    "🎊 **SET WELCOME BONUS**\n\nCurrent: 💎{}\n\nEnter new welcome bonus amount (💎0-💎100):".format(
                        (await self.services['admin_settings'].get_settings()).welcome_bonus
                    ),
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_settings_menu")]])
                )
            elif data == "admin_set_limits":
                await query.edit_message_text(
                    "🔢 **SET DAILY LIMITS**\n\nConfigure daily limits for various actions:",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("📋 Max Daily Claims", callback_data="admin_set_max_claims")],
                        [InlineKeyboardButton("👥 Referral Limit", callback_data="admin_set_ref_limit")],
                        [InlineKeyboardButton("🔙 Back", callback_data="admin_settings_menu")]
                    ])
                )
            elif data == "admin_reset_settings":
                await query.edit_message_text(
                    "🔄 **RESET SETTINGS TO DEFAULTS**\n\n⚠️ **WARNING:** This will reset all bot settings to default values!\n\nAre you sure?",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("✅ Yes, Reset", callback_data="admin_confirm_reset")],
                        [InlineKeyboardButton("❌ Cancel", callback_data="admin_settings_menu")]
                    ])
                )
            elif data == "admin_export_settings":
                settings = await self.services['admin_settings'].get_settings()
                settings_text = f"""
📄 **CURRENT BOT SETTINGS**

💰 **Financial Settings:**
• Referral Reward: 💎{settings.referral_reward}
• Daily Bonus: 💎{settings.daily_bonus_amount}
• Min Withdrawal: 💎{settings.minimum_withdrawal}
• Withdrawal Cooldown: {settings.withdrawal_cooldown_hours}h

🎁 **Bonus Settings:**
• Welcome Bonus: 💎{settings.welcome_bonus}
• Max Daily Claims: {settings.max_daily_claims}
• Daily Referral Limit: {settings.referral_limit_per_day}

*Settings exported successfully*
                """
                await query.edit_message_text(
                    settings_text,
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_settings_menu")]])
                )
            elif data == "admin_reload_config":
                await self._handle_config_reload(query, context)

            # Task management callbacks
            elif data == "admin_tasks_create":
                await query.edit_message_text(
                    "📋 **CREATE NEW TASK**\n\nPlease enter the task title:",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_tasks")]])
                )
            elif data == "admin_tasks_list":
                await self._show_admin_tasks_list(query, context)
            elif data == "admin_tasks_stats":
                await self._show_task_statistics(query, context)

            # Broadcasting callbacks
            elif data == "admin_broadcast_create":
                await query.edit_message_text(
                    "📢 **CREATE BROADCAST**\n\nPlease enter your broadcast message:",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_broadcast")]])
                )
            elif data == "admin_broadcast_history":
                await self._show_broadcast_history(query, context)
            elif data == "admin_broadcast_stats":
                await self._show_broadcast_statistics(query, context)
            elif data == "admin_users_edit":
                await query.edit_message_text(
                    "✏️ **Edit User**\n\nPlease send the user ID of the user you want to edit.",
                    parse_mode='Markdown'
                )
            elif data == "admin_users_ban":
                await query.edit_message_text(
                    "🚫 **Ban/Unban User**\n\nPlease send the user ID of the user you want to ban or unban.",
                    parse_mode='Markdown'
                )
            elif data == "admin_users_delete":
                await query.edit_message_text(
                    "🗑️ **Delete User**\n\n⚠️ **WARNING:** This action is irreversible!\n\nPlease send the user ID of the user you want to delete.",
                    parse_mode='Markdown'
                )

            # Settings callbacks
            elif data.startswith("admin_set_"):
                setting_type = data.replace("admin_set_", "")
                await self._handle_setting_change(query, context, setting_type)

            # Task management callbacks
            elif data == "admin_task_list":
                await self._show_task_list(query, context)
            elif data == "admin_task_create":
                await self._show_task_creation_form(query, context)
            elif data == "admin_task_stats":
                await self._show_task_statistics(query, context)
            elif data == "admin_review_submissions":
                await self._show_pending_submissions(query, context)
            elif data.startswith("review_approve_"):
                submission_id = data.replace("review_approve_", "")
                await self._approve_submission(query, context, submission_id)
            elif data.startswith("review_reject_"):
                submission_id = data.replace("review_reject_", "")
                await self._reject_submission(query, context, submission_id)
            elif data.startswith("review_view_"):
                submission_id = data.replace("review_view_", "")
                await self._view_submission_details(query, context, submission_id)
            elif data == "admin_task_edit":
                await self._show_task_edit_menu(query, context)
            elif data.startswith("edit_task_"):
                task_id = data.replace("edit_task_", "")
                await self._show_task_edit_form(query, context, task_id)
            elif data.startswith("save_task_"):
                task_id = data.replace("save_task_", "")
                await self._save_task_edits(query, context, task_id)
            elif data.startswith("edit_field_"):
                field_info = data.replace("edit_field_", "")
                await self._handle_field_edit(query, context, field_info)
            elif data.startswith("toggle_status_"):
                task_id = data.replace("toggle_status_", "")
                await self._toggle_task_status(query, context, task_id)

            # Broadcasting callbacks
            elif data == "admin_broadcast_new":
                await self._show_broadcast_creation_form(query, context)
            elif data.startswith("admin_broadcast_target_"):
                target_group = data.replace("admin_broadcast_target_", "")
                await self._confirm_broadcast(query, context, target_group)
            elif data.startswith("admin_broadcast_send_"):
                target_group = data.replace("admin_broadcast_send_", "")
                await self._send_broadcast(query, context, target_group)
            elif data == "admin_broadcast_history":
                await self._show_broadcast_history(query, context)

            # Analytics callbacks
            elif data == "admin_report_activity":
                await self._show_activity_report(query, context)
            elif data == "admin_report_referrers":
                await self._show_top_referrers(query, context)
            elif data == "admin_report_financial":
                await self._show_financial_report(query, context)
            elif data == "admin_report_tasks":
                await self._show_task_statistics(query, context)

            # Admin logs pagination
            elif data.startswith("admin_logs_"):
                page = int(data.split("_")[-1])
                await self._show_admin_logs(query, context, page)
            elif data.startswith("admin_broadcast_history_"):
                page = int(data.split("_")[-1])
                await self._show_broadcast_history(query, context, page)

            else:
                await query.edit_message_text("🚧 Admin feature coming soon...")

        except Exception as e:
            logger.error(f"Error in admin callback: {e}")
            await query.answer("❌ An error occurred in admin panel.")

    async def _handle_balance_callbacks(self, query, context, data):
        """Handle balance-related callbacks"""
        try:
            user_id = query.from_user.id
            action = data.replace("balance_", "")

            if action == "refresh":
                await self._refresh_balance(query, context)
            else:
                await query.edit_message_text("🚧 Feature coming soon...")

        except Exception as e:
            logger.error(f"Error in balance callback: {e}")

    async def _refresh_balance(self, query, context):
        """Refresh and show current balance"""
        try:
            user_id = query.from_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for balance refresh: {e}")
                user = None

            if not user:
                balance_text = """
💰 **Your Balance**

Current Balance: **💎0.00**
Total Withdrawals: **💎0.00**

📈 Start referring friends to earn!
                """
            else:
                balance_text = f"""
💰 **Your Balance**

Current Balance: **💎{user.balance:.2f}**
Total Withdrawals: **💎{user.total_withdrawals:.2f}**

"""

                if user.balance >= Config.MINIMUM_WITHDRAWAL:
                    balance_text += f"✅ You can withdraw! (Min: 💎{Config.MINIMUM_WITHDRAWAL})"
                else:
                    needed = Config.MINIMUM_WITHDRAWAL - user.balance
                    balance_text += f"📈 Earn 💎{needed:.2f} more to withdraw!"

            # Create inline keyboard for balance actions
            keyboard = [
                [InlineKeyboardButton("📈 Transaction History", callback_data="balance_history")],
                [InlineKeyboardButton("� Withdraw 💟", callback_data="withdraw_menu")],
                [InlineKeyboardButton("🔄 Refresh", callback_data="balance_refresh")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                balance_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error refreshing balance: {e}")

    async def _copy_referral_link(self, query, context):
        """Show referral link for copying"""
        try:
            user_id = query.from_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referral link: {e}")
                user = None

            if not user:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"

            copy_text = f"""
📋 **Your Referral Link**

`{referral_link}`

**How to share:**
1. Copy the link above
2. Share with friends on social media
3. Earn 💎{Config.REFERRAL_REWARD} for each successful referral!

**Tips:**
• Share in groups and channels
• Add a personal message
• Explain the benefits
            """

            await query.edit_message_text(
                copy_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error copying referral link: {e}")

    async def _handle_withdrawal_callbacks(self, query, context, data):
        """Handle withdrawal-related callbacks"""
        await query.edit_message_text("🚧 Withdrawal features coming soon...")

    async def _handle_referral_menu_callback(self, query, context):
        """Handle referral menu callback from withdrawal center"""
        try:
            user_id = query.from_user.id

            # Get user data
            user = await self.services['user'].get_user(user_id)
            if not user:
                await query.edit_message_text("❌ User not found. Please start the bot with /start")
                return

            # Get referral statistics
            referral_count = await self.services['referral'].get_referral_count(user_id)
            total_referrals = await self.database.referrals.count_documents({'referrer_id': user_id})

            # Get dynamic referral reward
            referral_reward = await self.get_setting('referral_reward', Config.REFERRAL_REWARD)

            referral_text = f"""
👥 **REFERRAL PROGRAM** 👥

**Your Stats:**
• **Active Referrals:** {referral_count}
• **Total Invited:** {total_referrals}
• **Earnings per Friend:** 💎{referral_reward}

**Your Referral Link:**
`{Config.BOT_USERNAME}?start={user_id}`

**How it works:**
1. Share your link with friends
2. They join and start earning
3. You get 💎{referral_reward} for each friend
4. No limits - invite unlimited friends!

*Tap the button below to copy your link*
            """

            keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", callback_data="copy_referral_link")],
                [InlineKeyboardButton("🔙 Back to Withdrawal", callback_data="withdraw_menu")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in referral menu callback: {e}")
            await query.answer("❌ Failed to load referral menu.")

    async def _handle_daily_bonus_callback(self, query, context):
        """Handle daily bonus callback from withdrawal center"""
        try:
            user_id = query.from_user.id

            # Check if user can claim daily bonus
            can_claim, time_until_next = await self.services['user'].can_claim_daily_bonus(user_id)

            if can_claim:
                # Process daily bonus claim
                bonus_amount = await self.get_setting('daily_bonus_amount', Config.DAILY_BONUS_AMOUNT)

                # Update user balance
                await self.services['user'].add_balance(user_id, bonus_amount)

                # Create transaction record
                try:
                    await self.services['transaction'].create_transaction(
                        user_id=user_id,
                        transaction_type='daily_bonus',
                        amount=bonus_amount,
                        description=f"Daily bonus claimed"
                    )
                except Exception as e:
                    logger.error(f"Failed to create transaction: {e}")

                # Update last claim time
                await self.services['user'].update_last_daily_claim(user_id)

                success_text = f"""
🎁 **DAILY BONUS CLAIMED!** 🎁

✅ **Bonus:** +💎{bonus_amount}
🕐 **Next Claim:** 24 hours from now
💰 **Keep earning daily!**

*Come back tomorrow for another bonus!*
                """

                keyboard = [
                    [InlineKeyboardButton("💰 Check Balance", callback_data="balance_refresh")],
                    [InlineKeyboardButton("🔙 Back to Withdrawal", callback_data="withdraw_menu")]
                ]

            else:
                # Show time until next claim
                hours = int(time_until_next // 3600)
                minutes = int((time_until_next % 3600) // 60)

                wait_text = f"""
🎁 **DAILY BONUS** 🎁

⏰ **Already Claimed Today**
🕐 **Next Claim:** {hours}h {minutes}m

*Come back later to claim your daily bonus!*

**Other ways to earn:**
• Invite friends for instant rewards
• Complete available tasks
                """

                keyboard = [
                    [InlineKeyboardButton("👥 Invite Friends", callback_data="referral_menu")],
                    [InlineKeyboardButton("📋 View Tasks", callback_data="refresh_tasks")],
                    [InlineKeyboardButton("🔙 Back to Withdrawal", callback_data="withdraw_menu")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                success_text if can_claim else wait_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in daily bonus callback: {e}")
            await query.answer("❌ Failed to process daily bonus.")

    async def _handle_contact_admin_callback(self, query, context):
        """Handle contact admin callback from withdrawal center"""
        try:
            contact_text = """
📞 **CONTACT ADMIN** 📞

**For withdrawal requests, please contact:**

🔗 **Admin Contact:** @your_admin_username
📧 **Email:** <EMAIL>
💬 **Support Group:** @your_support_group

**Include in your message:**
• Your User ID: `{}`
• Current Balance: Check your balance first
• Preferred withdrawal method

*Admin will process your request within 24-48 hours*
            """.format(query.from_user.id)

            keyboard = [
                [InlineKeyboardButton("💰 Check Balance", callback_data="balance_refresh")],
                [InlineKeyboardButton("🔙 Back to Withdrawal", callback_data="withdraw_menu")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                contact_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in contact admin callback: {e}")
            await query.answer("❌ Failed to load contact information.")

    async def _handle_withdraw_menu_callback(self, query, context):
        """Handle withdraw menu callback - return to withdrawal center"""
        try:
            user_id = query.from_user.id

            # Get user data
            user = await self.services['user'].get_user(user_id)
            if not user:
                await query.edit_message_text("❌ User not found. Please start the bot with /start")
                return

            # Get dynamic minimum withdrawal amount
            minimum_withdrawal = await self.get_setting('minimum_withdrawal', Config.MINIMUM_WITHDRAWAL)
            referral_reward = await self.get_setting('referral_reward', Config.REFERRAL_REWARD)
            daily_bonus_amount = await self.get_setting('daily_bonus_amount', Config.DAILY_BONUS_AMOUNT)

            # Check balance against minimum withdrawal limit
            can_withdraw = user.balance >= minimum_withdrawal
            needed_amount = minimum_withdrawal - user.balance if not can_withdraw else 0

            if can_withdraw:
                withdraw_text = f"""
💟 **Withdrawal Available** 💟

💰 **Balance:** **💎{user.balance:.2f}**
✅ **Status:** Ready for withdrawal!
🎯 **Minimum:** 💎{minimum_withdrawal}

*Contact admin to process your withdrawal.*
                """

                keyboard = [
                    [InlineKeyboardButton("📞 Contact Admin", callback_data="contact_admin")]
                ]
            else:
                withdraw_text = f"""
💟 **Withdrawal Center** 💟

💰 **Balance:** **💎{user.balance:.2f}**
🎯 **Minimum:** **💎{minimum_withdrawal}**
📈 **Need:** **💎{needed_amount:.2f}** more

*Please complete the minimum amount criteria to unlock withdrawals!*
                """

                keyboard = [
                    [InlineKeyboardButton("👥 Invite Friends", callback_data="referral_menu")],
                    [InlineKeyboardButton("🎁 Daily Bonus", callback_data="daily_bonus")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                withdraw_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in withdraw menu callback: {e}")
            await query.answer("❌ Failed to load withdrawal menu.")

    async def _handle_channel_verification_callback(self, query, context):
        """Handle channel verification callback when user clicks 'I HAVE JOINED'"""
        try:
            user_id = query.from_user.id

            # Check if the message has text or is a photo message
            has_text = query.message.text is not None
            has_caption = query.message.caption is not None

            logger.info(f"Verification callback - Message type: text={has_text}, caption={has_caption}")

            # Show verification animation - handle both text and photo messages
            try:
                if has_text:
                    # Text message - can edit text directly
                    await query.edit_message_text("🔍 **Verifying membership...**")
                elif has_caption:
                    # Photo with caption - edit caption
                    await query.edit_message_caption("🔍 **Verifying membership...**")
                else:
                    # Photo without caption - delete and send new message
                    await query.message.delete()
                    verification_msg = await context.bot.send_message(
                        chat_id=query.message.chat.id,
                        text="🔍 **Verifying membership...**",
                        parse_mode='Markdown'
                    )
                    # Update query.message to point to new message for further operations
                    query.message = verification_msg
            except Exception as edit_error:
                logger.error(f"Error editing verification message: {edit_error}")
                # Fallback: send new message
                verification_msg = await context.bot.send_message(
                    chat_id=query.message.chat.id,
                    text="🔍 **Verifying membership...**",
                    parse_mode='Markdown'
                )
                query.message = verification_msg

            await asyncio.sleep(1)

            # Re-verify channel membership
            is_member = await self._verify_required_channels(user_id, context)

            if is_member:
                # User has joined both channels, proceed to welcome
                try:
                    await query.edit_message_text("✅ **Verification successful!**\n\nWelcome to the bot!")
                except:
                    # If edit fails, send new message
                    await query.message.delete()
                    await context.bot.send_message(
                        chat_id=query.message.chat.id,
                        text="✅ **Verification successful!**\n\nWelcome to the bot!",
                        parse_mode='Markdown'
                    )

                await asyncio.sleep(1)

                # Mark as verified in session
                session_key = f"channel_verified_{user_id}"
                context.user_data[session_key] = True

                # Get user data for welcome sequence
                user = query.from_user

                # Create mock update object for welcome sequence
                mock_update = type('MockUpdate', (), {
                    'effective_user': user,
                    'effective_chat': query.message.chat,
                    'message': query.message
                })()

                # Get pending referral code if any
                referral_code = context.user_data.get('pending_referral')
                if referral_code:
                    context.args = [referral_code.split('_')[0] if '_' in referral_code else referral_code]
                else:
                    context.args = []

                # Delete the verification message
                try:
                    await query.message.delete()
                except:
                    logger.warning("Could not delete verification message")

                # Start the normal welcome sequence
                await self._animated_welcome_sequence(mock_update, context, user)

            else:
                # User still hasn't joined all channels
                join_links = Config.get_join_links()
                retry_keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("📢 JOIN CHANNEL 1", url=join_links[0])],
                    [InlineKeyboardButton("📢 JOIN CHANNEL 2", url=join_links[1])],
                    [InlineKeyboardButton("✅ I HAVE JOINED", callback_data="verify_channels")]
                ])

                try:
                    await query.edit_message_text(
                        "❌ **Please join all channels by clicking these buttons**",
                        reply_markup=retry_keyboard
                    )
                except:
                    # If edit fails, delete and send new message
                    await query.message.delete()
                    await context.bot.send_message(
                        chat_id=query.message.chat.id,
                        text="❌ **Please join all channels by clicking these buttons**",
                        parse_mode='Markdown',
                        reply_markup=retry_keyboard
                    )

        except Exception as e:
            logger.error(f"Error in channel verification callback: {e}")
            try:
                await query.answer("❌ Verification failed. Please try again.")
            except:
                logger.error("Could not send callback answer")

    async def _handle_config_reload(self, query, context):
        """Handle configuration reload request"""
        try:
            user_id = query.from_user.id

            # Show reloading animation
            await query.edit_message_text("🔄 **Reloading configuration...**")
            await asyncio.sleep(1)

            # Reload configuration
            success = Config.reload_config()

            if success:
                # Get updated channel configuration
                channels = Config.get_required_channels()
                links = Config.get_join_links()

                await query.edit_message_text(
                    f"✅ **Configuration Reloaded Successfully!**\n\n"
                    f"**📺 Channel Configuration:**\n"
                    f"• Channel 1: `{channels[0]}`\n"
                    f"• Channel 2: `{channels[1]}`\n\n"
                    f"**🔗 Join Links:**\n"
                    f"• Link 1: {links[0]}\n"
                    f"• Link 2: {links[1]}\n\n"
                    f"**💰 Financial Settings:**\n"
                    f"• Referral Reward: 💎{Config.REFERRAL_REWARD}\n"
                    f"• Daily Bonus: 💎{Config.DAILY_BONUS_AMOUNT}\n"
                    f"• Min Withdrawal: 💎{Config.MINIMUM_WITHDRAWAL}\n\n"
                    f"*All changes from .env file have been applied*",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Settings", callback_data="admin_settings_menu")]])
                )

                # Log the reload action
                log_admin_action(user_id, "CONFIG_RELOADED", "Configuration reloaded from .env file")

            else:
                await query.edit_message_text(
                    "❌ **Configuration Reload Failed**\n\n"
                    "There was an error reloading the configuration. Please check the logs for details.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Settings", callback_data="admin_settings_menu")]])
                )

        except Exception as e:
            logger.error(f"Error in config reload: {e}")
            await query.answer("❌ Configuration reload failed.")

    async def _handle_admin_withdrawals_callbacks(self, query, context, data):
        """Handle admin withdrawals management callbacks"""
        try:
            action = data.replace("admin_withdrawals_", "")

            if action == "pending":
                # Show pending withdrawal requests
                pending_requests = await self.database.withdrawals.find({'status': 'pending'}).to_list(10)

                if not pending_requests:
                    await query.edit_message_text(
                        "📋 **PENDING WITHDRAWALS**\n\nNo pending withdrawal requests.",
                        parse_mode='Markdown',
                        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_withdrawals_menu")]])
                    )
                    return

                requests_text = "📋 **PENDING WITHDRAWAL REQUESTS**\n\n"
                for i, req in enumerate(pending_requests, 1):
                    requests_text += f"**{i}.** User ID: {req['user_id']}\n"
                    requests_text += f"   Amount: 💎{req['amount']:.2f}\n"
                    requests_text += f"   Date: {req['created_at'].strftime('%Y-%m-%d %H:%M')}\n\n"

                keyboard = [
                    [InlineKeyboardButton("🔄 Refresh", callback_data="admin_withdrawals_pending")],
                    [InlineKeyboardButton("🔙 Back", callback_data="admin_withdrawals_menu")]
                ]

                await query.edit_message_text(
                    requests_text,
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

            elif action == "completed":
                # Show completed withdrawals
                completed_requests = await self.database.withdrawals.find({'status': 'completed'}).sort('completed_at', -1).limit(10).to_list(10)

                if not completed_requests:
                    await query.edit_message_text(
                        "✅ **COMPLETED WITHDRAWALS**\n\nNo completed withdrawals yet.",
                        parse_mode='Markdown',
                        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_withdrawals_menu")]])
                    )
                    return

                completed_text = "✅ **COMPLETED WITHDRAWALS** (Last 10)\n\n"
                for i, req in enumerate(completed_requests, 1):
                    completed_text += f"**{i}.** User ID: {req['user_id']}\n"
                    completed_text += f"   Amount: 💎{req['amount']:.2f}\n"
                    completed_text += f"   Completed: {req['completed_at'].strftime('%Y-%m-%d %H:%M')}\n\n"

                keyboard = [
                    [InlineKeyboardButton("🔄 Refresh", callback_data="admin_withdrawals_completed")],
                    [InlineKeyboardButton("🔙 Back", callback_data="admin_withdrawals_menu")]
                ]

                await query.edit_message_text(
                    completed_text,
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

            elif action == "stats":
                # Show withdrawal statistics
                stats = await self._get_withdrawal_statistics()

                stats_text = f"""
📊 **WITHDRAWAL STATISTICS**

**Overview:**
• **Total Requests:** {stats['total_requests']}
• **Pending:** {stats['pending_count']}
• **Completed:** {stats['completed_count']}
• **Rejected:** {stats['rejected_count']}

**Financial:**
• **Total Withdrawn:** 💎{stats['total_withdrawn']:.2f}
• **Pending Amount:** 💎{stats['pending_amount']:.2f}
• **Average Withdrawal:** 💎{stats['average_withdrawal']:.2f}

**Performance:**
• **Completion Rate:** {stats['completion_rate']:.1f}%
• **Processing Time:** {stats['avg_processing_time']} hours
                """

                keyboard = [
                    [InlineKeyboardButton("🔄 Refresh", callback_data="admin_withdrawals_stats")],
                    [InlineKeyboardButton("🔙 Back", callback_data="admin_withdrawals_menu")]
                ]

                await query.edit_message_text(
                    stats_text,
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

            elif action == "menu":
                # Return to admin withdrawals menu
                await self._show_admin_withdrawals_menu_callback(query, context)

        except Exception as e:
            logger.error(f"Error in admin withdrawals callback: {e}")
            await query.answer("❌ Failed to process request.")

    async def _handle_settings_callbacks(self, query, context, data):
        """Handle settings-related callbacks"""
        await query.edit_message_text("🚧 Settings features coming soon...")

    # ==================== HELPER METHODS ====================

    async def _process_referral(self, referred_user_id: int, referrer_user_id: int, referral_code: str):
        """Process a new referral"""
        try:
            # Create referral record
            referral = await self.services['referral'].create_referral(
                referrer_user_id,
                referred_user_id,
                referral_code
            )

            if referral:
                # Complete the referral and give reward
                success = await self.services['referral'].complete_referral(referral.referral_id)

                if success:
                    # Notify referrer
                    try:
                        referrer = await self.services['user'].get_user(referrer_user_id)
                        if referrer:
                            notification_text = f"""
🎉 **New Referral!**

You earned 💎{Config.REFERRAL_REWARD} for referring a new user!

💰 Your new balance: 💎{referrer.balance:.2f}
👥 Total referrals: {referrer.referral_count}

Keep sharing your referral link to earn more! 🚀
                            """

                            await self.application.bot.send_message(
                                chat_id=referrer_user_id,
                                text=notification_text,
                                parse_mode='Markdown'
                            )
                    except Exception as e:
                        logger.error(f"Failed to notify referrer {referrer_user_id}: {e}")

                logger.info(f"Processed referral: {referrer_user_id} -> {referred_user_id}")

        except Exception as e:
            logger.error(f"Error processing referral: {e}")

    # ==================== ANIMATION FRAMES ====================

    def _get_loading_frame_1(self):
        """Initial loading frame"""
        return """
🎁 **GIFTS BOT STARTING...**

✨ Preparing amazing gifts for you!
▓░░░░ 25%
        """

    def _get_loading_frame(self, step):
        """Progressive loading frames"""
        frames = {
            2: {
                'progress': '▓▓░░░ 50%',
                'status': '🎁 Loading gift catalog...'
            },
            3: {
                'progress': '▓▓▓░░ 75%',
                'status': '🌟 Setting up rewards...'
            }
        }

        frame_data = frames.get(step, frames[3])

        return f"""
🎁 **GIFTS BOT STARTING...**

{frame_data['status']}
{frame_data['progress']}
        """

    def _get_features_loading_frame(self):
        """Features loading frame"""
        return """
🎁 **GIFTS BOT READY!**

🎊 Your gift account is ready!
▓▓▓▓▓ 100%
        """

    def _get_welcome_reveal_frame(self):
        """Welcome reveal frame"""
        return """
🎉 **WELCOME TO GIFTS BOT!** 🎉

✨ Start earning amazing gifts now!
🎁 Canva Pro • Spotify • Netflix & more!
        """

    def _get_final_welcome_message(self, user, db_user, referral_code):
        """Generate concise welcome message"""
        user_name = db_user.get_display_name() if db_user else user.first_name

        # Referral bonus message
        referral_bonus_msg = ""
        if referral_code:
            referral_bonus_msg = f"🎊 **Referral bonus activated!** You and your friend both earn extra! 🎁\n\n"

        # Concise welcome content
        balance_text = f"💎{db_user.balance:.2f}" if db_user else "💎0.00"
        welcome_content = f"""
💟 **Welcome, {user_name}!** 💟

This is a *PRO GIFTS BOT* from Titanium Channel!\n
*use this bot to get* - 

- _a lot of gifts from titanium channel
- paid subscriptions of bots
- pro of netflix, spotify, canva etc for FREE for 1 Year_

*Use the menu below🔻🔻*
        """

        return welcome_content

    # ==================== ADMIN PANEL ====================

    async def admin_panel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Main admin panel entry point"""
        try:
            user_id = update.effective_user.id

            # Check admin access
            if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                await update.message.reply_text("❌ Access denied. Admin privileges required.")
                return

            # Show main admin menu
            await self._show_admin_main_menu(update, context)

        except Exception as e:
            logger.error(f"Error in admin panel command: {e}")
            await update.message.reply_text("❌ Failed to access admin panel.")

    async def _show_admin_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main admin panel menu"""
        try:
            admin_text = """
🔧 **ADMIN PANEL** 🔧

**Welcome to the comprehensive admin control center.**

**Available Sections:**
• 👥 User Management - View, edit, ban/unban users
• ⚙️ Bot Settings - Configure financial and system settings
• 📋 Task Management - Create and manage user tasks
• 📢 Broadcasting - Send messages to users
• 📊 Analytics - View statistics and reports
• 🔒 System Health - Monitor bot performance

*Select a section below to get started:*
            """

            keyboard = [
                [InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
                 InlineKeyboardButton("⚙️ Bot Settings", callback_data="admin_settings_menu")],
                [InlineKeyboardButton("📋 Task Management", callback_data="admin_tasks"),
                 InlineKeyboardButton("📢 Broadcasting", callback_data="admin_broadcast")],
                [InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics"),
                 InlineKeyboardButton("🔒 System Health", callback_data="admin_health")],
                [InlineKeyboardButton("📋 Admin Logs", callback_data="admin_logs")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            if update.callback_query:
                await update.callback_query.edit_message_text(
                    admin_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    admin_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing admin main menu: {e}")
            await update.message.reply_text("❌ Failed to load admin menu.")

    async def _show_admin_main_menu_callback(self, query, context):
        """Show main admin panel menu for callback queries"""
        try:
            admin_text = """
🔧 **ADMIN PANEL** 🔧

**Welcome to the comprehensive admin control center.**

**Available Sections:**
• 👥 User Management - View, edit, ban/unban users
• ⚙️ Bot Settings - Configure financial and system settings
• 📋 Task Management - Create and manage user tasks
• 📢 Broadcasting - Send messages to users
• 📊 Analytics - View statistics and reports
• 🔒 System Health - Monitor bot performance

*Select a section below to get started:*
            """

            keyboard = [
                [InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
                 InlineKeyboardButton("⚙️ Bot Settings", callback_data="admin_settings_menu")],
                [InlineKeyboardButton("📋 Task Management", callback_data="admin_tasks"),
                 InlineKeyboardButton("📢 Broadcasting", callback_data="admin_broadcast")],
                [InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics"),
                 InlineKeyboardButton("🔒 System Health", callback_data="admin_health")],
                [InlineKeyboardButton("📋 Admin Logs", callback_data="admin_logs")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                admin_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing admin main menu callback: {e}")
            await query.answer("❌ Failed to load admin menu.")

    async def _show_user_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user management menu"""
        try:
            # Get basic user statistics
            stats = await self.services['admin_panel'].get_bot_statistics()
            user_stats = stats.get('users', {})

            menu_text = f"""
👥 **USER MANAGEMENT** 👥

**Current Statistics:**
• **Total Users:** {user_stats.get('total', 0)}
• **Active (7 days):** {user_stats.get('active_7_days', 0)}
• **Banned Users:** {user_stats.get('banned', 0)}
• **Activity Rate:** {user_stats.get('active_percentage', 0)}%

**Management Options:**
            """

            keyboard = [
                [InlineKeyboardButton("📋 View All Users", callback_data="admin_users_list"),
                 InlineKeyboardButton("🔍 Search Users", callback_data="admin_users_search")],
                [InlineKeyboardButton("✏️ Edit User", callback_data="admin_users_edit"),
                 InlineKeyboardButton("🚫 Ban/Unban User", callback_data="admin_users_ban")],
                [InlineKeyboardButton("🗑️ Delete User", callback_data="admin_users_delete"),
                 InlineKeyboardButton("📊 User Reports", callback_data="admin_users_reports")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing user management menu: {e}")
            await update.callback_query.answer("❌ Failed to load user management menu.")

    async def _show_user_management_menu_callback(self, query, context):
        """Show user management menu for callback queries"""
        try:
            # Get basic user statistics
            stats = await self.services['admin_panel'].get_bot_statistics()
            user_stats = stats.get('users', {})

            menu_text = f"""
👥 **USER MANAGEMENT** 👥

**Current Statistics:**
• **Total Users:** {user_stats.get('total', 0)}
• **Active (7 days):** {user_stats.get('active_7_days', 0)}
• **Banned Users:** {user_stats.get('banned', 0)}
• **Activity Rate:** {user_stats.get('active_percentage', 0)}%

**Management Options:**
            """

            keyboard = [
                [InlineKeyboardButton("📋 View All Users", callback_data="admin_users_list"),
                 InlineKeyboardButton("🔍 Search Users", callback_data="admin_users_search")],
                [InlineKeyboardButton("✏️ Edit User", callback_data="admin_users_edit"),
                 InlineKeyboardButton("🚫 Ban/Unban User", callback_data="admin_users_ban")],
                [InlineKeyboardButton("🗑️ Delete User", callback_data="admin_users_delete"),
                 InlineKeyboardButton("📊 User Reports", callback_data="admin_users_reports")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing user management menu callback: {e}")
            await query.answer("❌ Failed to load user management menu.")

    async def _show_users_list(self, update_or_query, context: ContextTypes.DEFAULT_TYPE, page: int = 1):
        """Show paginated users list"""
        try:
            users, total_pages, total_users = await self.services['admin_panel'].get_users_paginated(page, 5)

            if not users:
                if hasattr(update_or_query, 'callback_query'):
                    await update_or_query.callback_query.answer("No users found.")
                else:
                    await update_or_query.answer("No users found.")
                return

            list_text = f"👥 **USERS LIST** (Page {page}/{total_pages})\n\n"

            for i, user in enumerate(users, 1):
                status = "🚫 BANNED" if getattr(user, 'is_banned', False) else "✅ ACTIVE"
                list_text += f"**{i}.** {user.get_display_name()}\n"
                list_text += f"   ID: `{user.user_id}` | Balance: 💎{user.balance:.2f}\n"
                list_text += f"   Status: {status} | Referrals: {user.successful_referrals}\n\n"

            # Pagination buttons
            keyboard = []
            nav_buttons = []

            if page > 1:
                nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"admin_users_list_{page-1}"))
            if page < total_pages:
                nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"admin_users_list_{page+1}"))

            if nav_buttons:
                keyboard.append(nav_buttons)

            keyboard.append([InlineKeyboardButton("🔙 Back to User Management", callback_data="admin_users")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Handle both Update and CallbackQuery objects
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.edit_message_text(
                    list_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await update_or_query.edit_message_text(
                    list_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing users list: {e}")
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.answer("❌ Failed to load users list.")
            else:
                await update_or_query.answer("❌ Failed to load users list.")

    async def _show_user_reports(self, query, context):
        """Show user reports and statistics"""
        try:
            # Get user statistics
            stats = await self.services['admin_panel'].get_bot_statistics()
            user_stats = stats.get('users', {})
            financial_stats = stats.get('financial', {})

            reports_text = f"""
📊 **USER REPORTS & ANALYTICS**

**📈 User Growth:**
• Total Users: **{user_stats.get('total', 0)}**
• Active (7 days): **{user_stats.get('active_7_days', 0)}**
• New Today: **{user_stats.get('new_today', 0)}**
• Activity Rate: **{user_stats.get('active_percentage', 0)}%**

**💰 Financial Overview:**
• Total Balance: **💎{financial_stats.get('total_balance', 0):.2f}**
• Average Balance: **💎{financial_stats.get('average_balance', 0):.2f}**

**🚫 Moderation:**
• Banned Users: **{user_stats.get('banned', 0)}**
• Suspended: **{user_stats.get('suspended', 0)}**

*Reports updated in real-time*
            """

            keyboard = [
                [InlineKeyboardButton("📋 Export User List", callback_data="admin_export_users"),
                 InlineKeyboardButton("📊 Detailed Analytics", callback_data="admin_analytics")],
                [InlineKeyboardButton("🔙 Back to User Management", callback_data="admin_users")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                reports_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing user reports: {e}")
            await query.answer("❌ Failed to load user reports.")

    async def _show_admin_tasks_list(self, query, context):
        """Show list of admin tasks"""
        try:
            tasks = await self.services['admin_panel'].get_admin_tasks()

            if not tasks:
                await query.edit_message_text(
                    "📋 **ADMIN TASKS**\n\nNo tasks created yet.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_tasks")]])
                )
                return

            tasks_text = "📋 **ADMIN TASKS LIST**\n\n"
            for i, task in enumerate(tasks[:10], 1):
                status = "🟢 Active" if task.get('is_active', True) else "🔴 Inactive"
                tasks_text += f"{i}. **{task.get('title', 'Untitled')}**\n"
                tasks_text += f"   Reward: 💎{task.get('reward', 0)} | {status}\n"
                tasks_text += f"   Completions: {task.get('completion_count', 0)}\n\n"

            keyboard = [
                [InlineKeyboardButton("➕ Create Task", callback_data="admin_tasks_create")],
                [InlineKeyboardButton("📊 Task Statistics", callback_data="admin_tasks_stats")],
                [InlineKeyboardButton("🔙 Back", callback_data="admin_tasks")]
            ]

            await query.edit_message_text(
                tasks_text,
                parse_mode='Markdown',
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        except Exception as e:
            logger.error(f"Error showing admin tasks list: {e}")
            await query.answer("❌ Failed to load tasks list.")

    async def _show_task_statistics(self, query, context):
        """Show task statistics"""
        try:
            # Get statistics from task service
            stats = await self.services['task'].get_task_statistics()

            stats_text = f"""
📊 **TASK STATISTICS**

**📋 Task Overview:**
• Total Tasks: **{stats.get('total_tasks', 0)}**
• Active Tasks: **{stats.get('active_tasks', 0)}**
• Inactive Tasks: **{stats.get('total_tasks', 0) - stats.get('active_tasks', 0)}**

**✅ Completion Stats:**
• Total Completions: **{stats.get('total_completions', 0)}**
• Pending Reviews: **{stats.get('pending_reviews', 0)}**
• Rewards Distributed: **💎{stats.get('total_rewards_distributed', 0):.2f}**

*Statistics updated in real-time*
            """

            keyboard = [
                [InlineKeyboardButton("📋 View Tasks", callback_data="admin_task_list")],
                [InlineKeyboardButton("🔄 Refresh Stats", callback_data="admin_task_stats")],
                [InlineKeyboardButton("🔙 Back", callback_data="admin_tasks")]
            ]

            await query.edit_message_text(
                stats_text,
                parse_mode='Markdown',
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        except Exception as e:
            logger.error(f"Error showing task statistics: {e}")
            await query.answer("❌ Failed to load task statistics.")

    async def _show_broadcast_history(self, query, context):
        """Show broadcast history"""
        try:
            # Get recent broadcasts
            broadcasts = await self.services['admin_panel'].get_broadcast_history(limit=10)

            if not broadcasts:
                await query.edit_message_text(
                    "📢 **BROADCAST HISTORY**\n\nNo broadcasts sent yet.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_broadcast")]])
                )
                return

            history_text = "📢 **BROADCAST HISTORY**\n\n"
            for i, broadcast in enumerate(broadcasts, 1):
                sent_count = broadcast.get('sent_count', 0)
                target_count = broadcast.get('target_count', 0)
                success_rate = (sent_count / target_count * 100) if target_count > 0 else 0

                history_text += f"{i}. **{broadcast.get('message', 'No message')[:30]}...**\n"
                history_text += f"   Sent: {sent_count}/{target_count} ({success_rate:.1f}%)\n"
                history_text += f"   Date: {broadcast.get('created_at', 'Unknown')}\n\n"

            keyboard = [
                [InlineKeyboardButton("📊 Broadcast Stats", callback_data="admin_broadcast_stats")],
                [InlineKeyboardButton("🔙 Back", callback_data="admin_broadcast")]
            ]

            await query.edit_message_text(
                history_text,
                parse_mode='Markdown',
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        except Exception as e:
            logger.error(f"Error showing broadcast history: {e}")
            await query.answer("❌ Failed to load broadcast history.")

    async def _show_broadcast_statistics(self, query, context):
        """Show broadcast statistics"""
        try:
            stats = await self.services['admin_panel'].get_broadcast_statistics()

            stats_text = f"""
📊 **BROADCAST STATISTICS**

**📢 Broadcast Overview:**
• Total Broadcasts: **{stats.get('total_broadcasts', 0)}**
• Messages Sent: **{stats.get('total_messages_sent', 0)}**

**📈 Performance:**
• Last 24h: **{stats.get('broadcasts_24h', 0)} broadcasts**
• Last 7 days: **{stats.get('broadcasts_7d', 0)} broadcasts**
• Most Successful: **{stats.get('best_success_rate', 0):.1f}%**

*Statistics updated in real-time*
            """

            keyboard = [
                [InlineKeyboardButton("📢 View History", callback_data="admin_broadcast_history")],
                [InlineKeyboardButton("🔙 Back", callback_data="admin_broadcast")]
            ]

            await query.edit_message_text(
                stats_text,
                parse_mode='Markdown',
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        except Exception as e:
            logger.error(f"Error showing broadcast statistics: {e}")
            await query.answer("❌ Failed to load broadcast statistics.")

    async def _show_bot_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show bot settings management menu"""
        try:
            # Get current settings
            settings = await self.services['admin_settings'].get_settings()

            settings_text = f"""
⚙️ **BOT SETTINGS** ⚙️

**Current Configuration:**

**💰 Financial Settings:**
• Referral Reward: **💎{settings.referral_reward}**
• Daily Bonus: **💎{settings.daily_bonus_amount}**
• Min Withdrawal: **💎{settings.minimum_withdrawal}**
• Withdrawal Cooldown: **{settings.withdrawal_cooldown_hours}h**

**🎁 Bonus Settings:**
• Welcome Bonus: **💎{settings.welcome_bonus}**
• Max Daily Claims: **{settings.max_daily_claims}**
• Daily Referral Limit: **{settings.referral_limit_per_day}**

*Select a setting to modify:*
            """

            keyboard = [
                [InlineKeyboardButton("💰 Referral Reward", callback_data="admin_set_referral"),
                 InlineKeyboardButton("🎁 Daily Bonus", callback_data="admin_set_daily")],
                [InlineKeyboardButton("💸 Min Withdrawal", callback_data="admin_set_withdrawal"),
                 InlineKeyboardButton("⏰ Withdrawal Cooldown", callback_data="admin_set_cooldown")],
                [InlineKeyboardButton("🎊 Welcome Bonus", callback_data="admin_set_welcome"),
                 InlineKeyboardButton("🔢 Daily Limits", callback_data="admin_set_limits")],
                [InlineKeyboardButton("🔄 Reset to Defaults", callback_data="admin_reset_settings"),
                 InlineKeyboardButton("💾 Export Settings", callback_data="admin_export_settings")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing bot settings menu: {e}")
            await update.callback_query.answer("❌ Failed to load settings menu.")

    async def _show_bot_settings_menu_callback(self, query, context):
        """Show bot settings management menu for callback queries"""
        try:
            # Get current settings
            settings = await self.services['admin_settings'].get_settings()

            settings_text = f"""
⚙️ **BOT SETTINGS** ⚙️

**Current Configuration:**

**💰 Financial Settings:**
• Referral Reward: **💎{settings.referral_reward}**
• Daily Bonus: **💎{settings.daily_bonus_amount}**
• Min Withdrawal: **💎{settings.minimum_withdrawal}**
• Withdrawal Cooldown: **{settings.withdrawal_cooldown_hours}h**

**🎁 Bonus Settings:**
• Welcome Bonus: **💎{settings.welcome_bonus}**
• Max Daily Claims: **{settings.max_daily_claims}**
• Daily Referral Limit: **{settings.referral_limit_per_day}**

*Select a setting to modify:*
            """

            keyboard = [
                [InlineKeyboardButton("💰 Referral Reward", callback_data="admin_set_referral"),
                 InlineKeyboardButton("🎁 Daily Bonus", callback_data="admin_set_daily")],
                [InlineKeyboardButton("💸 Min Withdrawal", callback_data="admin_set_withdrawal"),
                 InlineKeyboardButton("⏰ Withdrawal Cooldown", callback_data="admin_set_cooldown")],
                [InlineKeyboardButton("🎊 Welcome Bonus", callback_data="admin_set_welcome"),
                 InlineKeyboardButton("🔢 Daily Limits", callback_data="admin_set_limits")],
                [InlineKeyboardButton("🔄 Reset to Defaults", callback_data="admin_reset_settings"),
                 InlineKeyboardButton("💾 Export Settings", callback_data="admin_export_settings")],
                [InlineKeyboardButton("🔄 Reload Config", callback_data="admin_reload_config")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing bot settings menu callback: {e}")
            await query.answer("❌ Failed to load settings menu.")

    async def _show_analytics_menu(self, update_or_query, context: ContextTypes.DEFAULT_TYPE):
        """Show analytics and reporting menu"""
        try:
            # Get bot statistics
            stats = await self.services['admin_panel'].get_bot_statistics()

            analytics_text = f"""
📊 **ANALYTICS DASHBOARD** 📊

**User Statistics:**
• Total Users: **{stats.get('users', {}).get('total', 0)}**
• Active (7 days): **{stats.get('users', {}).get('active_7_days', 0)}**
• Activity Rate: **{stats.get('users', {}).get('active_percentage', 0)}%**

**Financial Overview:**
• Total Balance: **💎{stats.get('financial', {}).get('total_balance', 0):.2f}**
• Average Balance: **💎{stats.get('financial', {}).get('average_balance', 0):.2f}**

**Activity Today:**
• Transactions: **{stats.get('activity', {}).get('today_transactions', 0)}**
• Total Referrals: **{stats.get('activity', {}).get('total_referrals', 0)}**

*Select a report to view:*
            """

            keyboard = [
                [InlineKeyboardButton("📈 User Activity Report", callback_data="admin_report_activity"),
                 InlineKeyboardButton("🏆 Top Referrers", callback_data="admin_report_referrers")],
                [InlineKeyboardButton("💰 Financial Report", callback_data="admin_report_financial"),
                 InlineKeyboardButton("📊 Task Statistics", callback_data="admin_report_tasks")],
                [InlineKeyboardButton("🔄 Refresh Stats", callback_data="admin_analytics"),
                 InlineKeyboardButton("📋 Export Data", callback_data="admin_export_data")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Handle both Update and CallbackQuery objects
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.edit_message_text(
                    analytics_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await update_or_query.edit_message_text(
                    analytics_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing analytics menu: {e}")
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.answer("❌ Failed to load analytics.")
            else:
                await update_or_query.answer("❌ Failed to load analytics.")

    async def _show_system_health(self, update_or_query, context: ContextTypes.DEFAULT_TYPE):
        """Show system health and monitoring"""
        try:
            health = await self.services['admin_panel'].get_system_health()

            health_icon = "✅" if health.get('database_healthy', False) else "❌"

            health_text = f"""
🔒 **SYSTEM HEALTH** 🔒

**Database Status:** {health_icon}
**Connection:** {'Healthy' if health.get('database_healthy', False) else 'Issues Detected'}

**Collection Sizes:**
• Users: **{health.get('collections', {}).get('users', 0):,}**
• Transactions: **{health.get('collections', {}).get('transactions', 0):,}**
• Admin Logs: **{health.get('collections', {}).get('admin_logs', 0):,}**

**Last Check:** {health.get('timestamp', 'Unknown')}

*System monitoring and maintenance tools:*
            """

            keyboard = [
                [InlineKeyboardButton("🔄 Refresh Health", callback_data="admin_health"),
                 InlineKeyboardButton("🧹 Cleanup Database", callback_data="admin_cleanup")],
                [InlineKeyboardButton("📊 Performance Metrics", callback_data="admin_performance"),
                 InlineKeyboardButton("🔧 Maintenance Mode", callback_data="admin_maintenance")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Handle both Update and CallbackQuery objects
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.edit_message_text(
                    health_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await update_or_query.edit_message_text(
                    health_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing system health: {e}")
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.answer("❌ Failed to load system health.")
            else:
                await update_or_query.answer("❌ Failed to load system health.")

    async def _show_admin_logs(self, update_or_query, context: ContextTypes.DEFAULT_TYPE, page: int = 1):
        """Show admin action logs"""
        try:
            logs, total_logs = await self.services['admin_panel'].get_admin_logs(page, 5)

            if not logs:
                no_logs_text = "📋 **ADMIN LOGS**\n\nNo admin actions logged yet."
                if hasattr(update_or_query, 'callback_query'):
                    await update_or_query.callback_query.edit_message_text(
                        no_logs_text,
                        parse_mode='Markdown'
                    )
                else:
                    await update_or_query.edit_message_text(
                        no_logs_text,
                        parse_mode='Markdown'
                    )
                return

            logs_text = f"📋 **ADMIN LOGS** (Page {page})\n\n"

            for log in logs:
                timestamp = log.get('timestamp', 'Unknown')
                admin_id = log.get('admin_id', 'Unknown')
                action = log.get('action', 'Unknown')
                details = log.get('details', 'No details')

                # Format timestamp
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%m/%d %H:%M')
                except:
                    time_str = timestamp[:16] if len(timestamp) > 16 else timestamp

                logs_text += f"**{time_str}** - Admin {admin_id}\n"
                logs_text += f"Action: `{action}`\n"
                logs_text += f"Details: {details[:50]}{'...' if len(details) > 50 else ''}\n\n"

            # Pagination
            keyboard = []
            nav_buttons = []

            if page > 1:
                nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"admin_logs_{page-1}"))
            if len(logs) == 5:  # Assume more pages if we got full page
                nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"admin_logs_{page+1}"))

            if nav_buttons:
                keyboard.append(nav_buttons)

            keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Handle both Update and CallbackQuery objects
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.edit_message_text(
                    logs_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await update_or_query.edit_message_text(
                    logs_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing admin logs: {e}")
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.answer("❌ Failed to load admin logs.")
            else:
                await update_or_query.answer("❌ Failed to load admin logs.")

    async def _show_task_management_menu(self, update_or_query, context: ContextTypes.DEFAULT_TYPE):
        """Show task management menu"""
        try:
            # Get task statistics from task service
            task_stats = await self.services['task'].get_task_statistics()

            menu_text = f"""
📋 **TASK MANAGEMENT** 📋

**Current Statistics:**
• **Total Tasks:** {task_stats.get('total_tasks', 0)}
• **Active Tasks:** {task_stats.get('active_tasks', 0)}
• **Total Completions:** {task_stats.get('total_completions', 0)}
• **Pending Reviews:** {task_stats.get('pending_reviews', 0)}
• **Rewards Distributed:** 💎{task_stats.get('total_rewards_distributed', 0):.2f}

**Management Options:**
            """

            keyboard = [
                [InlineKeyboardButton("➕ Create New Task", callback_data="admin_task_create"),
                 InlineKeyboardButton("📋 View All Tasks", callback_data="admin_task_list")],
                [InlineKeyboardButton("✏️ Edit Task", callback_data="admin_task_edit"),
                 InlineKeyboardButton("📸 Review Submissions", callback_data="admin_review_submissions")],
                [InlineKeyboardButton("📊 Task Statistics", callback_data="admin_task_stats"),
                 InlineKeyboardButton("🔄 Refresh Tasks", callback_data="admin_tasks")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Handle both Update and CallbackQuery objects
            try:
                if hasattr(update_or_query, 'callback_query'):
                    await update_or_query.callback_query.edit_message_text(
                        menu_text,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
                else:
                    await update_or_query.edit_message_text(
                        menu_text,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
            except Exception as edit_error:
                # Handle "Message is not modified" error
                if "message is not modified" in str(edit_error).lower():
                    logger.debug("Message content unchanged, skipping edit")
                    if hasattr(update_or_query, 'callback_query'):
                        await update_or_query.callback_query.answer()
                    else:
                        await update_or_query.answer()
                else:
                    raise edit_error

        except Exception as e:
            logger.error(f"Error showing task management menu: {e}")
            try:
                if hasattr(update_or_query, 'callback_query'):
                    await update_or_query.callback_query.answer("❌ Failed to load task management.")
                else:
                    await update_or_query.answer("❌ Failed to load task management.")
            except:
                pass

    async def _show_broadcast_menu(self, update_or_query, context: ContextTypes.DEFAULT_TYPE):
        """Show broadcasting menu"""
        try:
            # Get recent broadcast history
            broadcasts, total = await self.services['admin_panel'].get_broadcast_history(1, 3)

            menu_text = """
📢 **BROADCASTING CENTER** 📢

**Send messages to your users with targeting options.**

**Target Groups Available:**
• 🌍 All Users - Send to all active users
• ⚡ Active Users - Users active in last 7 days
• 💎 Premium Users - Users with high balance
• 🆕 New Users - Users joined in last 3 days

**Recent Broadcasts:**
            """

            if broadcasts:
                for broadcast in broadcasts:
                    status = broadcast.get('status', 'unknown')
                    target = broadcast.get('target_group', 'unknown')
                    sent = broadcast.get('sent_count', 0)
                    menu_text += f"• {target.title()} - {status.title()} ({sent} sent)\n"
            else:
                menu_text += "• No recent broadcasts\n"

            keyboard = [
                [InlineKeyboardButton("📤 Send New Broadcast", callback_data="admin_broadcast_new"),
                 InlineKeyboardButton("📋 Broadcast History", callback_data="admin_broadcast_history")],
                [InlineKeyboardButton("👥 Target Groups", callback_data="admin_broadcast_targets"),
                 InlineKeyboardButton("📊 Delivery Stats", callback_data="admin_broadcast_stats")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Handle both Update and CallbackQuery objects
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.edit_message_text(
                    menu_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await update_or_query.edit_message_text(
                    menu_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing broadcast menu: {e}")
            if hasattr(update_or_query, 'callback_query'):
                await update_or_query.callback_query.answer("❌ Failed to load broadcast menu.")
            else:
                await update_or_query.answer("❌ Failed to load broadcast menu.")

    async def _handle_setting_change(self, query, context, setting_type):
        """Handle setting change requests"""
        try:
            setting_prompts = {
                'referral': "💰 **Set Referral Reward**\n\nCurrent: 💎{}\n\nPlease send the new referral reward amount (💎1-💎1000):",
                'daily': "🎁 **Set Daily Bonus**\n\nCurrent: 💎{}\n\nPlease send the new daily bonus amount (💎1-💎100):",
                'withdrawal': "💸 **Set Minimum Withdrawal**\n\nCurrent: 💎{}\n\nPlease send the new minimum withdrawal amount (💎100-💎10000):",
                'cooldown': "⏰ **Set Withdrawal Cooldown**\n\nCurrent: {} hours\n\nPlease send the new cooldown period (1-168 hours):",
                'welcome': "🎊 **Set Welcome Bonus**\n\nCurrent: 💎{}\n\nPlease send the new welcome bonus amount (💎0-💎100):",
                'limits': "🔢 **Set Daily Limits**\n\nPlease specify which limit to change:\n• Max daily claims\n• Daily referral limit"
            }

            if setting_type in setting_prompts:
                # Get current settings
                settings = await self.services['admin_settings'].get_settings()

                if setting_type == 'referral':
                    current_value = settings.referral_reward
                elif setting_type == 'daily':
                    current_value = settings.daily_bonus_amount
                elif setting_type == 'withdrawal':
                    current_value = settings.minimum_withdrawal
                elif setting_type == 'cooldown':
                    current_value = settings.withdrawal_cooldown_hours
                elif setting_type == 'welcome':
                    current_value = settings.welcome_bonus
                else:
                    current_value = "Various"

                prompt_text = setting_prompts[setting_type].format(current_value)

                # Store the setting type in context for later processing
                context.user_data['admin_setting_change'] = setting_type

                keyboard = [[InlineKeyboardButton("🔙 Cancel", callback_data="admin_settings_menu")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    prompt_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await query.edit_message_text("❌ Unknown setting type.")

        except Exception as e:
            logger.error(f"Error handling setting change: {e}")
            await query.answer("❌ Failed to process setting change.")

    async def _process_admin_setting_change(self, update: Update, context: ContextTypes.DEFAULT_TYPE, value: str):
        """Process admin setting change from text input"""
        try:
            user_id = update.effective_user.id

            # Check admin access
            if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                await update.message.reply_text("❌ Access denied.")
                return

            setting_type = context.user_data.get('admin_setting_change')
            if not setting_type:
                return

            # Clear the setting change flag
            del context.user_data['admin_setting_change']

            # Validate and process the setting change
            success = False
            error_message = ""

            try:
                if setting_type == 'referral':
                    amount = float(value)
                    if 1 <= amount <= 1000:
                        await self.services['admin_settings'].update_setting('referral_reward', amount)
                        success = True
                        await self.services['admin_panel'].log_admin_action(
                            user_id, 'SETTING_CHANGED', f"Referral reward set to 💎{amount}"
                        )
                    else:
                        error_message = "Referral reward must be between 💎1 and 💎1000."

                elif setting_type == 'daily':
                    amount = float(value)
                    if 1 <= amount <= 100:
                        await self.services['admin_settings'].update_setting('daily_bonus_amount', amount)
                        success = True
                        await self.services['admin_panel'].log_admin_action(
                            user_id, 'SETTING_CHANGED', f"Daily bonus set to 💎{amount}"
                        )
                    else:
                        error_message = "Daily bonus must be between 💎1 and 💎100."

                elif setting_type == 'withdrawal':
                    amount = float(value)
                    if 100 <= amount <= 10000:
                        await self.services['admin_settings'].update_setting('minimum_withdrawal', amount)
                        success = True
                        await self.services['admin_panel'].log_admin_action(
                            user_id, 'SETTING_CHANGED', f"Minimum withdrawal set to 💎{amount}"
                        )
                    else:
                        error_message = "Minimum withdrawal must be between 💎100 and 💎10,000."

                elif setting_type == 'cooldown':
                    hours = int(value)
                    if 1 <= hours <= 168:
                        await self.services['admin_settings'].update_setting('withdrawal_cooldown_hours', hours)
                        success = True
                        await self.services['admin_panel'].log_admin_action(
                            user_id, 'SETTING_CHANGED', f"Withdrawal cooldown set to {hours} hours"
                        )
                    else:
                        error_message = "Cooldown must be between 1 and 168 hours."

                elif setting_type == 'welcome':
                    amount = float(value)
                    if 0 <= amount <= 100:
                        await self.services['admin_settings'].update_setting('welcome_bonus', amount)
                        success = True
                        await self.services['admin_panel'].log_admin_action(
                            user_id, 'SETTING_CHANGED', f"Welcome bonus set to 💎{amount}"
                        )
                    else:
                        error_message = "Welcome bonus must be between 💎0 and 💎100."

            except ValueError:
                error_message = "Invalid number format. Please enter a valid number."
            except Exception as e:
                logger.error(f"Error updating setting: {e}")
                error_message = "Failed to update setting. Please try again."

            # Send response
            if success:
                keyboard = [[InlineKeyboardButton("🔙 Back to Settings", callback_data="admin_settings_menu")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    f"✅ **Setting Updated Successfully!**\n\nThe {setting_type} setting has been updated.",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                keyboard = [
                    [InlineKeyboardButton("🔄 Try Again", callback_data=f"admin_set_{setting_type}")],
                    [InlineKeyboardButton("🔙 Back to Settings", callback_data="admin_settings_menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    f"❌ **Error:** {error_message}",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error processing admin setting change: {e}")
            await update.message.reply_text("❌ Failed to process setting change.")

    async def _show_task_list(self, query, context):
        """Show list of all admin tasks"""
        try:
            # Get tasks using the task service
            tasks = await self.services['task'].get_active_tasks()

            # Also get inactive tasks
            all_tasks_cursor = self.database.tasks.find({})
            all_tasks = []
            async for task_data in all_tasks_cursor:
                task_data.pop('_id', None)
                all_tasks.append(task_data)

            if not all_tasks:
                await query.edit_message_text(
                    "📋 **TASK LIST**\n\nNo tasks created yet.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Task Management", callback_data="admin_tasks")]])
                )
                return

            task_text = "📋 **ALL TASKS**\n\n"

            for i, task in enumerate(all_tasks, 1):
                status = "✅ Active" if task.get('is_active', True) else "❌ Inactive"
                completions = task.get('completion_count', 0)

                task_text += f"**{i}.** {task.get('task_name', 'Untitled')}\n"
                task_text += f"   Reward: 💎{task.get('reward_amount', 0)} | {status}\n"
                task_text += f"   Completions: {completions} | Type: {task.get('task_type', 'unknown')}\n"
                task_text += f"   ID: `{task.get('task_id', 'unknown')}`\n\n"

            keyboard = [
                [InlineKeyboardButton("➕ Create New Task", callback_data="admin_task_create")],
                [InlineKeyboardButton("🔙 Back to Task Management", callback_data="admin_tasks")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                task_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing task list: {e}")
            await query.answer("❌ Failed to load task list.")

    async def _show_task_creation_form(self, query, context):
        """Show enhanced task creation form with task type selection"""
        try:
            form_text = """
➕ **CREATE NEW TASK** ➕

**Task Creation Flow:**
1. Enter task button name (what users will see)
2. Select task type
3. Configure task-specific settings
4. Set reward amount
5. Review and create task

Please enter the task button name (max 30 characters):
            """

            # Store task creation state
            context.user_data['admin_task_creation'] = {'step': 'button_name'}

            keyboard = [[InlineKeyboardButton("🔙 Cancel", callback_data="admin_tasks")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                form_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing task creation form: {e}")
            await query.answer("❌ Failed to load task creation form.")

    async def _process_task_creation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, value: str):
        """Process enhanced task creation steps"""
        try:
            user_id = update.effective_user.id

            # Check admin access
            if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                await update.message.reply_text("❌ Access denied.")
                return

            task_data = context.user_data.get('admin_task_creation', {})
            current_step = task_data.get('step')

            if current_step == 'button_name':
                if len(value) > 30:
                    await update.message.reply_text("❌ Button name too long. Maximum 30 characters.")
                    return

                task_data['button_name'] = value
                task_data['step'] = 'type_selection'
                context.user_data['admin_task_creation'] = task_data

                # Show task type selection
                keyboard = [
                    [InlineKeyboardButton("📺 Join Channel", callback_data="task_type_join_channel")],
                    [InlineKeyboardButton("📸 Submit Image", callback_data="task_type_submit_image")],
                    [InlineKeyboardButton("🔙 Cancel", callback_data="admin_tasks")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    f"✅ Button name set: **{value}**\n\nSelect the task type:",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

            elif current_step == 'channel_id':
                # Validate channel ID format
                if not (value.startswith('@') or value.startswith('-100')):
                    await update.message.reply_text("❌ Invalid channel ID. Use format: @channelname or -100xxxxxxxxx")
                    return

                task_data['channel_id'] = value
                task_data['step'] = 'channel_link'
                context.user_data['admin_task_creation'] = task_data

                await update.message.reply_text(
                    f"✅ Channel ID set: **{value}**\n\nNow send the public join link for the channel:",
                    parse_mode='Markdown'
                )

            elif current_step == 'channel_link':
                if not value.startswith('https://t.me/'):
                    await update.message.reply_text("❌ Invalid link format. Must start with https://t.me/")
                    return

                task_data['join_link'] = value
                task_data['step'] = 'reward'
                context.user_data['admin_task_creation'] = task_data

                await update.message.reply_text(
                    f"✅ Join link set: **{value}**\n\nNow send the reward amount (💎1-💎500):",
                    parse_mode='Markdown'
                )

            elif current_step == 'image_caption':
                task_data['instructions'] = value
                task_data['step'] = 'reward'
                context.user_data['admin_task_creation'] = task_data

                await update.message.reply_text(
                    f"✅ Instructions set: **{value}**\n\nNow send the reward amount (💎1-💎500):",
                    parse_mode='Markdown'
                )

            elif current_step == 'reward':
                try:
                    reward = float(value)
                    if not (1 <= reward <= 500):
                        await update.message.reply_text("❌ Reward must be between 💎1 and 💎500.")
                        return

                    task_data['reward'] = reward
                    context.user_data['admin_task_creation'] = task_data

                    # Show task summary and create
                    await self._show_task_creation_summary(update, context, task_data)

                except ValueError:
                    await update.message.reply_text("❌ Invalid reward amount. Please enter a number.")

        except Exception as e:
            logger.error(f"Error processing task creation: {e}")
            await update.message.reply_text("❌ Failed to process task creation.")

    async def _handle_task_type_selection(self, query, context, task_type: str):
        """Handle task type selection"""
        try:
            task_data = context.user_data.get('admin_task_creation', {})

            if task_type == "join_channel":
                task_data['task_type'] = 'join_channel'
                task_data['step'] = 'channel_id'
                context.user_data['admin_task_creation'] = task_data

                await query.edit_message_text(
                    f"✅ Task type: **Join Channel**\n\nPlease send the channel ID (format: @channelname or -100xxxxxxxxx):",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Cancel", callback_data="admin_tasks")]])
                )

            elif task_type == "submit_image":
                task_data['task_type'] = 'submit_image'
                task_data['step'] = 'reference_image'
                context.user_data['admin_task_creation'] = task_data

                await query.edit_message_text(
                    f"✅ Task type: **Submit Image**\n\nPlease upload the reference image that users should replicate:",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Cancel", callback_data="admin_tasks")]])
                )

        except Exception as e:
            logger.error(f"Error handling task type selection: {e}")
            await query.answer("❌ Failed to process task type selection.")

    async def _show_task_creation_summary(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_data: Dict[str, Any]):
        """Show task creation summary and confirmation"""
        try:
            task_type = task_data.get('task_type')

            # Log the task data for debugging
            logger.info(f"Task creation summary data: {task_data}")

            summary_text = f"""
📋 **TASK CREATION SUMMARY**

**Basic Information:**
• **Button Name:** {task_data.get('button_name')}
• **Task Type:** {task_type.replace('_', ' ').title()}
• **Reward:** 💎{task_data.get('reward')}

"""

            if task_type == 'join_channel':
                summary_text += f"""**Channel Task Details:**
• **Channel ID:** {task_data.get('channel_id')}
• **Join Link:** {task_data.get('join_link')}

"""
            elif task_type == 'submit_image':
                summary_text += f"""**Image Task Details:**
• **Instructions:** {task_data.get('instructions')}
• **Reference Image:** {"Uploaded" if task_data.get('reference_image_url') else "Not provided"}
• **Verification:** Manual Review

"""

            summary_text += "Confirm task creation?"

            keyboard = [
                [InlineKeyboardButton("✅ Create Task", callback_data="task_create_confirm")],
                [InlineKeyboardButton("🔙 Cancel", callback_data="admin_tasks")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                summary_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing task creation summary: {e}")
            logger.error(f"Task data: {task_data}")
            await update.message.reply_text("❌ Failed to show task summary.")

    async def _create_task_from_data(self, query, context):
        """Create task from stored data"""
        try:
            user_id = query.from_user.id
            task_data = context.user_data.get('admin_task_creation', {})

            from src.models.task import TaskType, VerificationMode

            # Map task type string to enum
            task_type_map = {
                'join_channel': TaskType.JOIN_CHANNEL,
                'submit_image': TaskType.SUBMIT_IMAGE
            }

            task_type = task_type_map.get(task_data.get('task_type'))
            if not task_type:
                await query.edit_message_text("❌ Invalid task type.")
                return

            # Prepare task creation parameters
            task_params = {
                'task_name': task_data.get('button_name'),
                'task_type': task_type,
                'reward_amount': task_data.get('reward'),
                'is_active': True
            }

            # Add type-specific parameters
            if task_type == TaskType.JOIN_CHANNEL:
                task_params.update({
                    'description': f"Join channel {task_data.get('channel_id', '')} to earn rewards",
                    'channel_id': task_data.get('channel_id'),
                    'join_link': task_data.get('join_link')
                })
            elif task_type == TaskType.SUBMIT_IMAGE:
                task_params.update({
                    'description': task_data.get('instructions', 'Submit an image to complete this task'),
                    'instructions': task_data.get('instructions'),
                    'reference_image_url': task_data.get('reference_image_url'),
                    'verification_mode': VerificationMode.MANUAL_REVIEW
                })

            # Create the task
            task = await self.services['task'].create_task(user_id, **task_params)

            # Clear task creation state
            del context.user_data['admin_task_creation']

            if task:
                success_text = f"""
✅ **TASK CREATED SUCCESSFULLY!**

**Task Details:**
• **Name:** {task.task_name}
• **Type:** {task.task_type.value.replace('_', ' ').title()}
• **Reward:** 💎{task.reward_amount}
• **Task ID:** `{task.task_id}`

The task is now active and available to users.
                """

                keyboard = [[InlineKeyboardButton("🔙 Back to Task Management", callback_data="admin_tasks")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    success_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await query.edit_message_text("❌ Failed to create task. Please try again.")

        except Exception as e:
            logger.error(f"Error creating task from data: {e}")
            await query.answer("❌ Failed to create task.")

    # ==================== USER TASK HANDLERS ====================

    async def _handle_task_action(self, query, context, task_id: str):
        """Handle general task action"""
        try:
            user_id = query.from_user.id

            # Get task details
            task = await self.services['task'].get_task(task_id)
            if not task:
                await query.edit_message_text("❌ Task not found.")
                return

            # Get user task status
            user_task = await self.services['task'].get_user_task(user_id, task_id)

            if user_task and user_task.status.value == 'completed':
                await query.edit_message_text("✅ You have already completed this task!")
                return

            # Route based on task type
            from src.models.task import TaskType

            if task.task_type == TaskType.JOIN_CHANNEL:
                await self._handle_join_channel_task(query, context, task_id)
            elif task.task_type == TaskType.SUBMIT_IMAGE:
                await self._handle_image_submission_task(query, context, task_id)
            else:
                await query.edit_message_text("🚧 This task type is not yet supported.")

        except Exception as e:
            logger.error(f"Error handling task action: {e}")
            await query.answer("❌ Failed to process task.")

    async def _handle_join_channel_task(self, query, context, task_id: str):
        """Handle join channel task"""
        try:
            user_id = query.from_user.id

            # Get task details
            task = await self.services['task'].get_task(task_id)
            if not task:
                await query.edit_message_text("❌ Task not found.")
                return

            # Start the task if not already started
            await self.services['task'].start_user_task(user_id, task_id)

            task_text = f"""
📺 **{task.task_name}**

**Reward:** 💎{task.reward_amount}

**Instructions:**
{task.description or 'Join the specified channel to complete this task.'}

**Channel:** {task.channel_username or task.channel_id}

**Steps:**
1. Click "Join Channel" to open the channel
2. Join the channel
3. Click "Verify Membership" to confirm

⚠️ **Important:** You must actually join the channel for verification to work.
            """

            keyboard = [
                [InlineKeyboardButton("📺 Join Channel", url=task.join_link)],
                [InlineKeyboardButton("✅ Verify Membership", callback_data=f"task_verify_{task_id}")],
                [InlineKeyboardButton("🔙 Back to Tasks", callback_data="refresh_tasks")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                task_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error handling join channel task: {e}")
            await query.answer("❌ Failed to load channel task.")

    async def _verify_channel_membership(self, query, context, task_id: str):
        """Verify channel membership for join channel task"""
        try:
            user_id = query.from_user.id

            # Get task details
            task = await self.services['task'].get_task(task_id)
            if not task:
                await query.edit_message_text("❌ Task not found.")
                return

            # Show verification animation
            checking_msg = await query.edit_message_text("🔍 Checking membership...")

            await asyncio.sleep(1)
            await checking_msg.edit_text("🔍 Verifying with Telegram...")

            await asyncio.sleep(1)

            # Verify membership using Telegram Bot API
            is_member = await self.services['task'].verify_channel_membership(
                context.bot, user_id, task.channel_id
            )

            if is_member:
                # Complete the task
                success = await self.services['task'].complete_user_task(user_id, task_id)

                if success:
                    await checking_msg.edit_text(
                        f"✅ **Task Completed!**\n\n💎{task.reward_amount} has been added to your balance!\n\nThank you for joining our channel! 🎉",
                        parse_mode='Markdown'
                    )
                else:
                    await checking_msg.edit_text("❌ Failed to complete task. Please try again.")
            else:
                keyboard = [
                    [InlineKeyboardButton("📺 Join Channel", url=task.join_link)],
                    [InlineKeyboardButton("🔄 Try Again", callback_data=f"task_verify_{task_id}")],
                    [InlineKeyboardButton("🔙 Back to Tasks", callback_data="refresh_tasks")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await checking_msg.edit_text(
                    "❌ **You haven't joined the channel yet!**\n\nPlease join the channel first, then click 'Try Again'.",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error verifying channel membership: {e}")
            await query.answer("❌ Failed to verify membership.")

    async def _handle_image_submission_task(self, query, context, task_id: str):
        """Handle image submission task"""
        try:
            user_id = query.from_user.id

            # Get task details
            task = await self.services['task'].get_task(task_id)
            if not task:
                await query.edit_message_text("❌ Task not found.")
                return

            # Check if already submitted and pending review
            user_task = await self.services['task'].get_user_task(user_id, task_id)
            if user_task and user_task.status.value == 'pending_review':
                await query.edit_message_text(
                    "⏳ **Task Already Submitted**\n\nYour submission is under review. Please wait for admin approval.",
                    parse_mode='Markdown'
                )
                return

            # Start the task if not already started
            await self.services['task'].start_user_task(user_id, task_id)

            # Show reference image and instructions
            task_text = f"""
📸 **{task.task_name}**

**Reward:** 💎{task.reward_amount}

**Instructions:**
{task.instructions or 'Submit a screenshot as proof of completion.'}

⚠️ **Warning:** Submitting fake or incorrect screenshots may result in permanent ban.

Click "I Have Completed This Task" to submit your proof.
            """

            keyboard = [
                [InlineKeyboardButton("📸 I Have Completed This Task", callback_data=f"task_submit_{task_id}")],
                [InlineKeyboardButton("🔙 Back to Tasks", callback_data="refresh_tasks")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send reference image if available
            if task.reference_image_url:
                try:
                    await query.edit_message_text("📸 **Reference Image:**")
                    await context.bot.send_photo(
                        chat_id=query.message.chat_id,
                        photo=task.reference_image_url,
                        caption=task_text,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
                except:
                    # Fallback if image fails
                    await query.edit_message_text(
                        task_text,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
            else:
                await query.edit_message_text(
                    task_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error handling image submission task: {e}")
            await query.answer("❌ Failed to load image task.")

    async def _start_image_submission(self, query, context, task_id: str):
        """Start image submission process with rate limiting"""
        try:
            user_id = query.from_user.id

            # Check rate limiting for task submissions
            if not await self._check_task_submission_rate_limit(user_id):
                await query.edit_message_text(
                    "⏰ **Rate Limit Exceeded**\n\nYou can only submit 3 tasks per hour. Please wait before submitting again.",
                    parse_mode='Markdown'
                )
                return

            # Check if user is banned
            if await self._is_user_banned_from_tasks(user_id):
                await query.edit_message_text(
                    "🚫 **Submission Blocked**\n\nYour task submission privileges have been suspended due to repeated violations.",
                    parse_mode='Markdown'
                )
                return

            # Store submission state
            context.user_data['user_task_submission'] = {
                'task_id': task_id,
                'user_id': user_id,
                'started_at': datetime.now(timezone.utc).isoformat()
            }

            await query.edit_message_text(
                "📸 **Upload Your Screenshot**\n\nPlease upload your screenshot or proof image now.\n\n⚠️ Make sure the image clearly shows completion of the required task.\n\n**Warning:** Submitting fake or incorrect screenshots may result in permanent ban.",
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error starting image submission: {e}")
            await query.answer("❌ Failed to start submission.")

    # ==================== ADMIN REVIEW SYSTEM ====================

    async def _show_pending_submissions(self, query, context):
        """Show pending task submissions for admin review"""
        try:
            # Get pending submissions
            submissions = await self.services['task'].get_pending_submissions(limit=10)

            if not submissions:
                await query.edit_message_text(
                    "📸 **TASK SUBMISSIONS REVIEW**\n\nNo pending submissions to review.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_tasks")]])
                )
                return

            review_text = f"📸 **PENDING SUBMISSIONS** ({len(submissions)})\n\n"

            keyboard = []
            for i, submission_data in enumerate(submissions[:5], 1):  # Show first 5
                submission = submission_data['submission']
                task = submission_data['task']
                user = submission_data['user']

                user_name = user.get('first_name', 'Unknown') if user else 'Unknown'
                task_name = task.task_name if task else 'Unknown Task'

                review_text += f"**{i}.** {task_name}\n"
                review_text += f"   User: {user_name} (ID: {submission.user_id})\n"
                review_text += f"   Submitted: {submission.submitted_at.strftime('%Y-%m-%d %H:%M')}\n\n"

                keyboard.append([InlineKeyboardButton(
                    f"📋 Review #{i}",
                    callback_data=f"review_view_{submission.submission_id}"
                )])

            keyboard.append([InlineKeyboardButton("🔙 Back to Task Management", callback_data="admin_tasks")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                review_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing pending submissions: {e}")
            await query.answer("❌ Failed to load submissions.")

    async def _view_submission_details(self, query, context, submission_id: str):
        """View detailed submission for review"""
        try:
            # Get submission details
            submissions = await self.services['task'].get_pending_submissions()
            submission_data = next((s for s in submissions if s['submission'].submission_id == submission_id), None)

            if not submission_data:
                await query.edit_message_text("❌ Submission not found.")
                return

            submission = submission_data['submission']
            task = submission_data['task']
            user = submission_data['user']

            user_name = user.get('first_name', 'Unknown') if user else 'Unknown'
            username = user.get('username', 'N/A') if user else 'N/A'

            details_text = f"""
📸 **SUBMISSION REVIEW**

**Task:** {task.task_name if task else 'Unknown'}
**Reward:** 💎{task.reward_amount if task else 0}

**User Details:**
• Name: {user_name}
• Username: @{username}
• User ID: `{submission.user_id}`

**Submission:**
• Submitted: {submission.submitted_at.strftime('%Y-%m-%d %H:%M UTC')}
• Type: {submission.submission_type}

**Instructions:**
{task.instructions if task else 'No instructions available'}
            """

            keyboard = [
                [InlineKeyboardButton("✅ Approve", callback_data=f"review_approve_{submission_id}"),
                 InlineKeyboardButton("❌ Reject", callback_data=f"review_reject_{submission_id}")],
                [InlineKeyboardButton("🔙 Back to Reviews", callback_data="admin_review_submissions")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send the submitted image first
            if submission.image_urls:
                try:
                    await context.bot.send_photo(
                        chat_id=query.message.chat_id,
                        photo=submission.image_urls[0],
                        caption="📸 **Submitted Image:**"
                    )
                except:
                    pass  # Continue even if image fails

            await query.edit_message_text(
                details_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error viewing submission details: {e}")
            await query.answer("❌ Failed to load submission details.")

    async def _approve_submission(self, query, context, submission_id: str):
        """Approve a task submission"""
        try:
            admin_id = query.from_user.id

            # Approve the submission
            success = await self.services['task'].approve_submission(
                submission_id, admin_id, "Approved by admin"
            )

            if success:
                await query.edit_message_text(
                    "✅ **Submission Approved!**\n\nThe user has been notified and rewarded.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Reviews", callback_data="admin_review_submissions")]])
                )

                # Send notification to user (you'd implement this based on your notification system)
                # await self._notify_user_submission_approved(submission.user_id, task_name)

            else:
                await query.edit_message_text("❌ Failed to approve submission.")

        except Exception as e:
            logger.error(f"Error approving submission: {e}")
            await query.answer("❌ Failed to approve submission.")

    async def _reject_submission(self, query, context, submission_id: str):
        """Reject a task submission"""
        try:
            admin_id = query.from_user.id

            # Reject the submission
            success = await self.services['task'].reject_submission(
                submission_id, admin_id, "Submission does not meet requirements"
            )

            if success:
                await query.edit_message_text(
                    "❌ **Submission Rejected!**\n\nThe user has been notified.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Reviews", callback_data="admin_review_submissions")]])
                )

                # Send notification to user (you'd implement this based on your notification system)
                # await self._notify_user_submission_rejected(submission.user_id, task_name)

            else:
                await query.edit_message_text("❌ Failed to reject submission.")

        except Exception as e:
            logger.error(f"Error rejecting submission: {e}")
            await query.answer("❌ Failed to reject submission.")

    # ==================== TASK EDITING SYSTEM ====================

    async def _show_task_edit_menu(self, query, context):
        """Show task edit menu with list of tasks to edit"""
        try:
            # Get all tasks
            tasks = await self.services['task'].get_active_tasks()

            if not tasks:
                await query.edit_message_text(
                    "✏️ **EDIT TASK**\n\nNo tasks available to edit.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data="admin_tasks")]])
                )
                return

            edit_text = "✏️ **EDIT TASK**\n\nSelect a task to edit:\n\n"

            keyboard = []
            for task in tasks:
                button_text = f"{task.task_name} (💎{task.reward_amount})"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=f"edit_task_{task.task_id}")])

            keyboard.append([InlineKeyboardButton("🔙 Back to Task Management", callback_data="admin_tasks")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                edit_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing task edit menu: {e}")
            await query.answer("❌ Failed to load task edit menu.")

    async def _show_task_edit_form(self, query, context, task_id: str):
        """Show form to edit a specific task"""
        try:
            # Get task details
            task = await self.services['task'].get_task(task_id)
            if not task:
                await query.edit_message_text("❌ Task not found.")
                return

            # Store task data in context for editing
            context.user_data['task_edit'] = {
                'task_id': task_id,
                'original': {
                    'task_name': task.task_name,
                    'description': task.description or '',
                    'reward_amount': task.reward_amount,
                    'is_active': task.is_active
                },
                'changes': {}
            }

            edit_text = f"""
✏️ **EDIT TASK**

**Current Task Details:**
• **Name:** {task.task_name}
• **Type:** {task.task_type.value.replace('_', ' ').title()}
• **Reward:** 💎{task.reward_amount}
• **Status:** {"Active" if task.is_active else "Inactive"}
• **Description:** {task.description or 'No description'}

**What would you like to edit?**
            """

            keyboard = [
                [InlineKeyboardButton("✏️ Edit Name", callback_data=f"edit_field_name_{task_id}")],
                [InlineKeyboardButton("✏️ Edit Description", callback_data=f"edit_field_description_{task_id}")],
                [InlineKeyboardButton("✏️ Edit Reward", callback_data=f"edit_field_reward_{task_id}")],
                [InlineKeyboardButton("🔄 Toggle Status", callback_data=f"toggle_status_{task_id}")],
                [InlineKeyboardButton("💾 Save Changes", callback_data=f"save_task_{task_id}")],
                [InlineKeyboardButton("🔙 Cancel", callback_data="admin_task_edit")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                edit_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing task edit form: {e}")
            await query.answer("❌ Failed to load task edit form.")

    async def _save_task_edits(self, query, context, task_id: str):
        """Save task edits to database"""
        try:
            user_id = query.from_user.id

            # Get edit data
            edit_data = context.user_data.get('task_edit', {})
            changes = edit_data.get('changes', {})

            if not changes:
                await query.answer("No changes to save.")
                return

            # Update task in database
            success = await self.services['task'].update_task(task_id, changes)

            if success:
                # Clear edit data
                if 'task_edit' in context.user_data:
                    del context.user_data['task_edit']

                await query.edit_message_text(
                    "✅ **Task Updated Successfully!**\n\nThe changes have been saved.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Task Management", callback_data="admin_tasks")]])
                )
            else:
                await query.edit_message_text(
                    "❌ **Failed to Update Task**\n\nPlease try again.",
                    parse_mode='Markdown',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back", callback_data=f"edit_task_{task_id}")]])
                )

        except Exception as e:
            logger.error(f"Error saving task edits: {e}")
            await query.answer("❌ Failed to save task edits.")

    async def _handle_field_edit(self, query, context, field_info: str):
        """Handle field edit request"""
        try:
            # Parse field_info: "name_task123" or "description_task123" or "reward_task123"
            parts = field_info.split('_', 1)
            if len(parts) != 2:
                await query.answer("❌ Invalid field edit request.")
                return

            field_name, task_id = parts

            # Store edit context
            context.user_data['field_edit'] = {
                'field': field_name,
                'task_id': task_id
            }

            field_prompts = {
                'name': 'Enter the new task name (max 30 characters):',
                'description': 'Enter the new task description (max 200 characters):',
                'reward': 'Enter the new reward amount (💎1-💎500):'
            }

            prompt = field_prompts.get(field_name, 'Enter the new value:')

            await query.edit_message_text(
                f"✏️ **EDIT {field_name.upper()}**\n\n{prompt}",
                parse_mode='Markdown',
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Cancel", callback_data=f"edit_task_{task_id}")]])
            )

        except Exception as e:
            logger.error(f"Error handling field edit: {e}")
            await query.answer("❌ Failed to handle field edit.")

    async def _toggle_task_status(self, query, context, task_id: str):
        """Toggle task active/inactive status"""
        try:
            # Get current task
            task = await self.services['task'].get_task(task_id)
            if not task:
                await query.answer("❌ Task not found.")
                return

            # Toggle status
            new_status = not task.is_active
            success = await self.services['task'].update_task(task_id, {'is_active': new_status})

            if success:
                status_text = "activated" if new_status else "deactivated"
                await query.answer(f"✅ Task {status_text} successfully!")

                # Refresh the edit form
                await self._show_task_edit_form(query, context, task_id)
            else:
                await query.answer("❌ Failed to update task status.")

        except Exception as e:
            logger.error(f"Error toggling task status: {e}")
            await query.answer("❌ Failed to toggle task status.")

    async def _process_field_edit(self, update: Update, context: ContextTypes.DEFAULT_TYPE, value: str):
        """Process field edit input"""
        try:
            user_id = update.effective_user.id

            # Check admin access
            if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                await update.message.reply_text("❌ Access denied.")
                return

            field_edit_data = context.user_data.get('field_edit', {})
            field_name = field_edit_data.get('field')
            task_id = field_edit_data.get('task_id')

            if not field_name or not task_id:
                await update.message.reply_text("❌ Invalid edit session.")
                return

            # Validate input based on field type
            if field_name == 'name':
                if len(value) > 30:
                    await update.message.reply_text("❌ Task name too long. Maximum 30 characters.")
                    return
                update_data = {'task_name': value}

            elif field_name == 'description':
                if len(value) > 200:
                    await update.message.reply_text("❌ Description too long. Maximum 200 characters.")
                    return
                update_data = {'description': value}

            elif field_name == 'reward':
                try:
                    reward = float(value)
                    if not (1 <= reward <= 500):
                        await update.message.reply_text("❌ Reward must be between 💎1 and 💎500.")
                        return
                    update_data = {'reward_amount': reward}
                except ValueError:
                    await update.message.reply_text("❌ Invalid reward amount. Please enter a number.")
                    return
            else:
                await update.message.reply_text("❌ Invalid field type.")
                return

            # Update the task
            success = await self.services['task'].update_task(task_id, update_data)

            # Clear field edit state
            del context.user_data['field_edit']

            if success:
                keyboard = [[InlineKeyboardButton("🔙 Back to Edit Task", callback_data=f"edit_task_{task_id}")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    f"✅ **{field_name.title()} Updated Successfully!**\n\nNew value: {value}",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await update.message.reply_text("❌ Failed to update task. Please try again.")

        except Exception as e:
            logger.error(f"Error processing field edit: {e}")
            await update.message.reply_text("❌ Failed to process field edit.")

    # ==================== SECURITY AND RATE LIMITING ====================

    async def _check_task_submission_rate_limit(self, user_id: int) -> bool:
        """Check if user can submit tasks (rate limiting)"""
        try:
            # Check submissions in the last hour
            one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)

            recent_submissions = await self.database.task_submissions.count_documents({
                'user_id': user_id,
                'submitted_at': {'$gte': one_hour_ago.isoformat()}
            })

            return recent_submissions < 3  # Max 3 submissions per hour

        except Exception as e:
            logger.error(f"Error checking rate limit for user {user_id}: {e}")
            return True  # Allow on error to avoid blocking legitimate users

    async def _is_user_banned_from_tasks(self, user_id: int) -> bool:
        """Check if user is banned from task submissions"""
        try:
            user = await self.services['user'].get_user(user_id)
            if not user:
                return False

            # Check if user is generally banned
            if user.is_banned:
                return True

            # Check for task-specific ban (you could implement this in user model)
            # For now, check if user has too many rejected submissions
            rejected_count = await self.database.user_tasks.count_documents({
                'user_id': user_id,
                'status': 'rejected'
            })

            return rejected_count >= 5  # Ban after 5 rejections

        except Exception as e:
            logger.error(f"Error checking task ban status for user {user_id}: {e}")
            return False

    async def _log_task_action(self, user_id: int, action: str, task_id: str, details: str = None):
        """Log task-related actions for audit trail"""
        try:
            log_entry = {
                'user_id': user_id,
                'action': action,
                'task_id': task_id,
                'details': details,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'ip_address': None,  # You could capture this from request context
                'user_agent': None   # You could capture this from request context
            }

            await self.database.db.task_audit_logs.insert_one(log_entry)

        except Exception as e:
            logger.error(f"Error logging task action: {e}")

    async def _validate_task_submission(self, user_id: int, task_id: str, image_data: dict) -> dict:
        """Validate task submission data"""
        try:
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': []
            }

            # Check if task exists and is active
            task = await self.services['task'].get_task(task_id)
            if not task:
                validation_result['valid'] = False
                validation_result['errors'].append("Task not found")
                return validation_result

            if not task.is_active:
                validation_result['valid'] = False
                validation_result['errors'].append("Task is no longer active")
                return validation_result

            # Check if user already completed this task
            user_task = await self.services['task'].get_user_task(user_id, task_id)
            if user_task and user_task.status.value == 'completed':
                validation_result['valid'] = False
                validation_result['errors'].append("Task already completed")
                return validation_result

            # Check if user has pending submission
            if user_task and user_task.status.value == 'pending_review':
                validation_result['valid'] = False
                validation_result['errors'].append("Task already submitted and pending review")
                return validation_result

            # Validate image data (basic checks)
            if not image_data.get('file_id'):
                validation_result['valid'] = False
                validation_result['errors'].append("No image provided")
                return validation_result

            # Check file size limits (Telegram handles this, but we can add additional checks)
            # Add any additional validation logic here

            return validation_result

        except Exception as e:
            logger.error(f"Error validating task submission: {e}")
            return {
                'valid': False,
                'errors': ['Validation failed due to system error'],
                'warnings': []
            }

    async def _show_broadcast_creation_form(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show broadcast creation form"""
        try:
            form_text = """
📢 **CREATE NEW BROADCAST** 📢

**Broadcasting Steps:**
1. Send your message text (supports **bold**, *italic*, `code`)
2. Choose target group
3. Confirm and send

**Target Groups Available:**
• 🌍 **All Users** - Send to all active users
• ⚡ **Active Users** - Users active in last 7 days
• 💎 **Premium Users** - Users with high balance (💎500+)
• 🆕 **New Users** - Users joined in last 3 days

*Please send your broadcast message:*
            """

            # Store broadcast creation state
            context.user_data['admin_broadcast_creation'] = {'step': 'message'}

            keyboard = [[InlineKeyboardButton("🔙 Cancel", callback_data="admin_broadcast")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                form_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing broadcast creation form: {e}")
            await update.callback_query.answer("❌ Failed to load broadcast form.")

    async def _process_broadcast_creation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message: str):
        """Process broadcast creation steps"""
        try:
            user_id = update.effective_user.id

            # Check admin access
            if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                await update.message.reply_text("❌ Access denied.")
                return

            broadcast_data = context.user_data.get('admin_broadcast_creation', {})
            current_step = broadcast_data.get('step')

            if current_step == 'message':
                if len(message) > 4000:
                    await update.message.reply_text("❌ Message too long. Maximum 4000 characters.")
                    return

                broadcast_data['message'] = message
                broadcast_data['step'] = 'target'
                context.user_data['admin_broadcast_creation'] = broadcast_data

                # Show target group selection
                target_text = f"""
✅ **Message Set:**
{message[:100]}{'...' if len(message) > 100 else ''}

**Select Target Group:**
                """

                keyboard = [
                    [InlineKeyboardButton("🌍 All Users", callback_data="admin_broadcast_target_all")],
                    [InlineKeyboardButton("⚡ Active Users", callback_data="admin_broadcast_target_active")],
                    [InlineKeyboardButton("💎 Premium Users", callback_data="admin_broadcast_target_premium")],
                    [InlineKeyboardButton("🆕 New Users", callback_data="admin_broadcast_target_new")],
                    [InlineKeyboardButton("🔙 Cancel", callback_data="admin_broadcast")]
                ]

                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    target_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error processing broadcast creation: {e}")
            await update.message.reply_text("❌ Failed to process broadcast creation.")

    async def _confirm_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_group: str):
        """Show broadcast confirmation"""
        try:
            broadcast_data = context.user_data.get('admin_broadcast_creation', {})
            message = broadcast_data.get('message', '')

            # Get target user count
            target_users = await self.services['admin_panel'].get_target_users(target_group)
            user_count = len(target_users)

            target_names = {
                'all': '🌍 All Users',
                'active': '⚡ Active Users',
                'premium': '💎 Premium Users',
                'new': '🆕 New Users'
            }

            confirm_text = f"""
📢 **CONFIRM BROADCAST** 📢

**Message Preview:**
{message}

**Target Group:** {target_names.get(target_group, target_group)}
**Recipients:** {user_count:,} users

**Are you sure you want to send this broadcast?**
            """

            keyboard = [
                [InlineKeyboardButton("✅ Send Broadcast", callback_data=f"admin_broadcast_send_{target_group}")],
                [InlineKeyboardButton("✏️ Edit Message", callback_data="admin_broadcast_new")],
                [InlineKeyboardButton("🔙 Cancel", callback_data="admin_broadcast")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                confirm_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing broadcast confirmation: {e}")
            await update.callback_query.answer("❌ Failed to show confirmation.")

    async def _send_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_group: str):
        """Send the broadcast to users"""
        try:
            user_id = update.effective_user.id
            broadcast_data = context.user_data.get('admin_broadcast_creation', {})
            message = broadcast_data.get('message', '')

            if not message:
                await update.callback_query.answer("❌ No message to send.")
                return

            # Create broadcast record
            broadcast_id = await self.services['admin_panel'].create_broadcast(
                admin_id=user_id,
                message=message,
                target_group=target_group
            )

            if not broadcast_id:
                await update.callback_query.answer("❌ Failed to create broadcast.")
                return

            # Get target users
            target_users = await self.services['admin_panel'].get_target_users(target_group)

            # Clear broadcast creation state
            del context.user_data['admin_broadcast_creation']

            # Show sending status
            status_text = f"""
📤 **BROADCAST SENDING** 📤

**Broadcast ID:** `{broadcast_id}`
**Target Group:** {target_group}
**Recipients:** {len(target_users):,} users

🔄 **Status:** Sending in progress...

*This may take a few minutes for large groups.*
            """

            keyboard = [[InlineKeyboardButton("🔙 Back to Broadcasting", callback_data="admin_broadcast")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                status_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            # Send broadcast to users (in background)
            asyncio.create_task(self._execute_broadcast(broadcast_id, message, target_users))

        except Exception as e:
            logger.error(f"Error sending broadcast: {e}")
            await update.callback_query.answer("❌ Failed to send broadcast.")

    async def _execute_broadcast(self, broadcast_id: str, message: str, target_users: list):
        """Execute the actual broadcast sending"""
        try:
            sent_count = 0
            delivered_count = 0
            failed_count = 0

            for user_id in target_users:
                try:
                    await self.application.bot.send_message(
                        chat_id=user_id,
                        text=message,
                        parse_mode='Markdown'
                    )
                    sent_count += 1
                    delivered_count += 1

                    # Small delay to avoid rate limiting
                    await asyncio.sleep(0.05)

                except Exception as e:
                    failed_count += 1
                    logger.debug(f"Failed to send broadcast to {user_id}: {e}")

                # Update stats every 100 messages
                if (sent_count + failed_count) % 100 == 0:
                    await self.services['admin_panel'].update_broadcast_stats(
                        broadcast_id, sent_count, delivered_count, failed_count
                    )

            # Final stats update
            await self.services['admin_panel'].update_broadcast_stats(
                broadcast_id, sent_count, delivered_count, failed_count
            )

            logger.info(f"Broadcast {broadcast_id} completed: {delivered_count} delivered, {failed_count} failed")

        except Exception as e:
            logger.error(f"Error executing broadcast {broadcast_id}: {e}")

    async def _show_broadcast_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page: int = 1):
        """Show broadcast history"""
        try:
            broadcasts, total = await self.services['admin_panel'].get_broadcast_history(page, 5)

            if not broadcasts:
                await update.callback_query.edit_message_text(
                    "📢 **BROADCAST HISTORY**\n\nNo broadcasts sent yet.",
                    parse_mode='Markdown'
                )
                return

            history_text = f"📢 **BROADCAST HISTORY** (Page {page})\n\n"

            for broadcast in broadcasts:
                broadcast_id = broadcast.get('broadcast_id', 'Unknown')
                target_group = broadcast.get('target_group', 'unknown')
                status = broadcast.get('status', 'unknown')
                sent_count = broadcast.get('sent_count', 0)
                delivered_count = broadcast.get('delivered_count', 0)
                failed_count = broadcast.get('failed_count', 0)
                message = broadcast.get('message', '')

                # Format timestamp
                created_at = broadcast.get('created_at', '')
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    time_str = dt.strftime('%m/%d %H:%M')
                except:
                    time_str = created_at[:16] if len(created_at) > 16 else created_at

                history_text += f"**{time_str}** - {target_group.title()}\n"
                history_text += f"Status: {status.title()} | Sent: {sent_count} | Delivered: {delivered_count}\n"
                history_text += f"Message: {message[:50]}{'...' if len(message) > 50 else ''}\n\n"

            # Pagination
            keyboard = []
            nav_buttons = []

            if page > 1:
                nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"admin_broadcast_history_{page-1}"))
            if len(broadcasts) == 5:  # Assume more pages if we got full page
                nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"admin_broadcast_history_{page+1}"))

            if nav_buttons:
                keyboard.append(nav_buttons)

            keyboard.append([InlineKeyboardButton("🔙 Back to Broadcasting", callback_data="admin_broadcast")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                history_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing broadcast history: {e}")
            await update.callback_query.answer("❌ Failed to load broadcast history.")

    async def _show_activity_report(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user activity report"""
        try:
            # Get 7-day activity report
            report = await self.services['admin_panel'].get_user_activity_report(7)

            report_text = f"""
📈 **USER ACTIVITY REPORT** 📈

**Period:** Last {report.get('period_days', 7)} days

**Daily Registrations:**
            """

            registrations = report.get('registrations', [])
            if registrations:
                for reg in registrations[-5:]:  # Show last 5 days
                    date = reg.get('_id', 'Unknown')
                    count = reg.get('count', 0)
                    report_text += f"• {date}: **{count}** new users\n"
            else:
                report_text += "• No new registrations in this period\n"

            report_text += "\n**Daily Transaction Activity:**\n"

            transactions = report.get('transactions', [])
            if transactions:
                for trans in transactions[-5:]:  # Show last 5 days
                    date = trans.get('_id', 'Unknown')
                    count = trans.get('count', 0)
                    amount = trans.get('total_amount', 0)
                    report_text += f"• {date}: **{count}** transactions (💎{amount:.2f})\n"
            else:
                report_text += "• No transactions in this period\n"

            report_text += f"\n*Generated: {report.get('generated_at', 'Unknown')}*"

            keyboard = [
                [InlineKeyboardButton("🔄 Refresh Report", callback_data="admin_report_activity")],
                [InlineKeyboardButton("🔙 Back to Analytics", callback_data="admin_analytics")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                report_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing activity report: {e}")
            await update.callback_query.answer("❌ Failed to load activity report.")

    async def _show_top_referrers(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show top referrers report"""
        try:
            top_referrers = await self.services['admin_panel'].get_top_referrers(10)

            if not top_referrers:
                await update.callback_query.edit_message_text(
                    "🏆 **TOP REFERRERS**\n\nNo referrals recorded yet.",
                    parse_mode='Markdown'
                )
                return

            report_text = "🏆 **TOP REFERRERS** 🏆\n\n"

            for i, referrer in enumerate(top_referrers, 1):
                name = referrer.get('first_name', 'Unknown')
                username = referrer.get('username', '')
                user_id = referrer.get('user_id', 'Unknown')
                referrals = referrer.get('successful_referrals', 0)
                balance = referrer.get('balance', 0)

                username_text = f"@{username}" if username else f"ID: {user_id}"

                report_text += f"**{i}.** {name} ({username_text})\n"
                report_text += f"   Referrals: **{referrals}** | Balance: 💎{balance:.2f}\n\n"

            keyboard = [
                [InlineKeyboardButton("🔄 Refresh Report", callback_data="admin_report_referrers")],
                [InlineKeyboardButton("🔙 Back to Analytics", callback_data="admin_analytics")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                report_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing top referrers: {e}")
            await update.callback_query.answer("❌ Failed to load top referrers.")

    async def _show_financial_report(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show financial overview report"""
        try:
            stats = await self.services['admin_panel'].get_bot_statistics()
            financial = stats.get('financial', {})
            users = stats.get('users', {})

            report_text = f"""
💰 **FINANCIAL REPORT** 💰

**Overall Financial Health:**
• **Total User Balance:** 💎{financial.get('total_balance', 0):.2f}
• **Average Balance per User:** 💎{financial.get('average_balance', 0):.2f}

**User Distribution:**
• **Total Users:** {users.get('total', 0):,}
• **Active Users (7d):** {users.get('active_7_days', 0):,}
• **Activity Rate:** {users.get('active_percentage', 0):.1f}%

**Key Metrics:**
• **Engagement Rate:** {users.get('active_percentage', 0):.1f}%
• **Avg. Balance per Active User:** 💎{(financial.get('total_balance', 0) / max(users.get('active_7_days', 1), 1)):.2f}

*Generated: {stats.get('generated_at', 'Unknown')}*
            """

            keyboard = [
                [InlineKeyboardButton("🔄 Refresh Report", callback_data="admin_report_financial")],
                [InlineKeyboardButton("🔙 Back to Analytics", callback_data="admin_analytics")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                report_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing financial report: {e}")
            await update.callback_query.answer("❌ Failed to load financial report.")

    # ==================== ERROR HANDLER ====================

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors"""
        logger.error(f"Update {update} caused error {context.error}")

        # Try to send error message to user
        if update and update.effective_chat:
            try:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="❌ An error occurred. Please try again later."
                )
            except:
                pass  # Ignore if we can't send the message

# ==================== MAIN EXECUTION ====================

def main():
    """Main function to run the complete bot"""
    try:
        logger.info("🤖 Starting Complete Telegram Referral Earning Bot...")
        logger.info("🔄 Mode: Long Polling (no webhook/domain required)")

        # Validate configuration
        Config.validate_config()
        logger.info("✅ Configuration validated")

        # Create bot instance
        bot = FinalBotApp()

        # Create application
        application = Application.builder().token(Config.BOT_TOKEN).build()
        bot.application = application

        # Add handlers
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("help", bot.help_command))
        application.add_handler(CommandHandler("balance", bot.balance_command))

        # Admin commands
        application.add_handler(CommandHandler("admin", bot.admin_panel_command))
        application.add_handler(CommandHandler("admin_settings", bot.admin_settings_command))
        application.add_handler(CommandHandler("set_referral_reward", bot.set_referral_reward_command))

        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_message))
        application.add_handler(MessageHandler(filters.PHOTO, bot.handle_photo))
        application.add_handler(CallbackQueryHandler(bot.handle_callback))
        application.add_error_handler(bot.error_handler)

        logger.info("✅ All handlers added successfully")

        # Initialize async components in a separate task
        async def init_and_run():
            await bot.initialize_async_components()
            logger.info("🚀 Starting bot in long polling mode...")
            logger.info("✅ Bot is running! Press Ctrl+C to stop.")
            logger.info("ℹ️ No webhook, domain, or SSL configuration required.")
            logger.info(f"🤖 Bot username: @{Config.BOT_USERNAME}")

        # Run the initialization
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(init_and_run())

        # Run the bot
        application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )

    except KeyboardInterrupt:
        logger.info("👋 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Bot error: {e}")
        raise

if __name__ == "__main__":
    main()
