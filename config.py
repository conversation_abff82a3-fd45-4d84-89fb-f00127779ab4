import os
from dotenv import load_dotenv
from typing import List, Optional
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class Config:
    """Configuration class for the Telegram Referral Bot"""
    
    # Bot Configuration
    BOT_TOKEN = os.getenv('BOT_TOKEN')
    BOT_USERNAME = os.getenv('BOT_USERNAME')
    
    # Database Configuration
    MONGODB_URI = os.getenv('MONGODB_URI')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'referral_bot_db')
    
    # Admin Configuration
    ADMIN_USER_IDS = [int(id.strip()) for id in os.getenv('ADMIN_USER_IDS', '').split(',') if id.strip()]
    ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin123')
    
    # Bot Settings
    REFERRAL_REWARD = int(os.getenv('REFERRAL_REWARD', 10))
    DAILY_BONUS_AMOUNT = int(os.getenv('DAILY_BONUS_AMOUNT', 8))
    MINIMUM_WITHDRAWAL = int(os.getenv('MINIMUM_WITHDRAWAL', 1500))
    WITHDRAWAL_COOLDOWN_HOURS = int(os.getenv('WITHDRAWAL_COOLDOWN_HOURS', 24))
    
    # Security
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
    RATE_LIMIT_MESSAGES = int(os.getenv('RATE_LIMIT_MESSAGES', 20))
    RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', 60))
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'bot.log')
    
    # Channel Settings - Forced Subscription Configuration
    REQUIRED_CHANNEL_1 = int(os.getenv('REQUIRED_CHANNEL_1', '-1001296547211'))
    REQUIRED_CHANNEL_2 = int(os.getenv('REQUIRED_CHANNEL_2', '-1002414699235'))
    JOIN_LINK_1 = os.getenv('JOIN_LINK_1', 'https://t.me/+ec_CC8b-gUxjNjQ1')
    JOIN_LINK_2 = os.getenv('JOIN_LINK_2', 'https://t.me/+0vJ8rUZLPTE2ZDhl')

    # Legacy channel setting (kept for backward compatibility)
    REQUIRED_CHANNELS = [ch.strip() for ch in os.getenv('REQUIRED_CHANNELS', '').split(',') if ch.strip()]

    # Bot Mode - Always use long polling (no webhook configuration)
    USE_POLLING = True
    
    # Currency
    CURRENCY_SYMBOL = '💎'
    CURRENCY_NAME = 'INR'
    
    # Bot Messages
    WELCOME_MESSAGE = """
🎉 Welcome to the Referral Earning Bot! 🎉

💰 Earn {currency}{referral_reward} for each successful referral!
🎁 Get daily bonuses!
💎 Withdraw at {currency}{min_withdrawal} minimum!

Start earning now by inviting your friends! 🚀
    """.format(
        currency=CURRENCY_SYMBOL,
        referral_reward=REFERRAL_REWARD,
        min_withdrawal=MINIMUM_WITHDRAWAL
    )
    
    # Product Catalog (can be managed through admin panel)
    DEFAULT_PRODUCTS = [
        {
            'name': 'Canva Pro (1 Month)',
            'price': 500,
            'description': 'Premium Canva subscription with all features unlocked',
            'category': 'Design Tools'
        },
        {
            'name': 'Spotify Premium (1 Month)',
            'price': 500,
            'description': 'Ad-free music streaming with offline downloads',
            'category': 'Entertainment'
        },
        {
            'name': 'Netflix Premium (1 Month)',
            'price': 500,
            'description': 'Premium Netflix subscription with 4K streaming',
            'category': 'Entertainment'
        },
        {
            'name': 'Adobe Creative Cloud (1 Month)',
            'price': 500,
            'description': 'Access to all Adobe creative applications',
            'category': 'Design Tools'
        }
    ]
    
    @classmethod
    def reload_config(cls):
        """Reload configuration from environment variables"""
        try:
            logger.info("🔄 Reloading configuration from environment variables...")

            # Reload .env file
            load_dotenv(override=True)

            # Update channel configuration
            cls.REQUIRED_CHANNEL_1 = int(os.getenv('REQUIRED_CHANNEL_1', '-1001296547211'))
            cls.REQUIRED_CHANNEL_2 = int(os.getenv('REQUIRED_CHANNEL_2', '-1002414699235'))
            cls.JOIN_LINK_1 = os.getenv('JOIN_LINK_1', 'https://t.me/+ec_CC8b-gUxjNjQ1')
            cls.JOIN_LINK_2 = os.getenv('JOIN_LINK_2', 'https://t.me/+0vJ8rUZLPTE2ZDhl')

            # Update other dynamic settings
            cls.REFERRAL_REWARD = int(os.getenv('REFERRAL_REWARD', 10))
            cls.DAILY_BONUS_AMOUNT = int(os.getenv('DAILY_BONUS_AMOUNT', 8))
            cls.MINIMUM_WITHDRAWAL = int(os.getenv('MINIMUM_WITHDRAWAL', 1500))
            cls.WITHDRAWAL_COOLDOWN_HOURS = int(os.getenv('WITHDRAWAL_COOLDOWN_HOURS', 24))

            logger.info(f"✅ Configuration reloaded successfully")
            logger.info(f"   - Channel 1: {cls.REQUIRED_CHANNEL_1}")
            logger.info(f"   - Channel 2: {cls.REQUIRED_CHANNEL_2}")
            logger.info(f"   - Join Link 1: {cls.JOIN_LINK_1}")
            logger.info(f"   - Join Link 2: {cls.JOIN_LINK_2}")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to reload configuration: {e}")
            return False

    @classmethod
    def get_required_channels(cls):
        """Get list of required channel IDs"""
        return [cls.REQUIRED_CHANNEL_1, cls.REQUIRED_CHANNEL_2]

    @classmethod
    def get_join_links(cls):
        """Get list of join links"""
        return [cls.JOIN_LINK_1, cls.JOIN_LINK_2]

    @classmethod
    def validate_config(cls):
        """Validate required configuration"""
        required_vars = ['BOT_TOKEN', 'MONGODB_URI']
        missing_vars = []

        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

        # Validate channel configuration
        try:
            channels = cls.get_required_channels()
            links = cls.get_join_links()

            if len(channels) != 2:
                logger.warning("⚠️ Expected 2 required channels, got {len(channels)}")

            if len(links) != 2:
                logger.warning("⚠️ Expected 2 join links, got {len(links)}")

            logger.info(f"✅ Channel configuration validated: {len(channels)} channels, {len(links)} links")

        except Exception as e:
            logger.error(f"❌ Channel configuration validation failed: {e}")

        return True
