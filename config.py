import os
from dotenv import load_dotenv
from typing import List

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the Telegram Referral Bot"""
    
    # Bot Configuration
    BOT_TOKEN = os.getenv('BOT_TOKEN')
    BOT_USERNAME = os.getenv('BOT_USERNAME')
    
    # Database Configuration
    MONGODB_URI = os.getenv('MONGODB_URI')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'referral_bot_db')
    
    # Admin Configuration
    ADMIN_USER_IDS = [int(id.strip()) for id in os.getenv('ADMIN_USER_IDS', '').split(',') if id.strip()]
    ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin123')
    
    # Bot Settings
    REFERRAL_REWARD = int(os.getenv('REFERRAL_REWARD', 10))
    DAILY_BONUS_AMOUNT = int(os.getenv('DAILY_BONUS_AMOUNT', 5))
    MINIMUM_WITHDRAWAL = int(os.getenv('MINIMUM_WITHDRAWAL', 500))
    WITHDRAWAL_COOLDOWN_HOURS = int(os.getenv('WITHDRAWAL_COOLDOWN_HOURS', 24))
    
    # Security
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
    RATE_LIMIT_MESSAGES = int(os.getenv('RATE_LIMIT_MESSAGES', 20))
    RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', 60))
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'bot.log')
    
    # Channel Settings
    REQUIRED_CHANNELS = [ch.strip() for ch in os.getenv('REQUIRED_CHANNELS', '').split(',') if ch.strip()]

    # Bot Mode - Always use long polling (no webhook configuration)
    USE_POLLING = True
    
    # Currency
    CURRENCY_SYMBOL = '💎'
    CURRENCY_NAME = 'INR'
    
    # Bot Messages
    WELCOME_MESSAGE = """
🎉 Welcome to the Referral Earning Bot! 🎉

💰 Earn {currency}{referral_reward} for each successful referral!
🎁 Get daily bonuses!
💎 Withdraw at {currency}{min_withdrawal} minimum!

Start earning now by inviting your friends! 🚀
    """.format(
        currency=CURRENCY_SYMBOL,
        referral_reward=REFERRAL_REWARD,
        min_withdrawal=MINIMUM_WITHDRAWAL
    )
    
    # Product Catalog (can be managed through admin panel)
    DEFAULT_PRODUCTS = [
        {
            'name': 'Canva Pro (1 Month)',
            'price': 500,
            'description': 'Premium Canva subscription with all features unlocked',
            'category': 'Design Tools'
        },
        {
            'name': 'Spotify Premium (1 Month)',
            'price': 500,
            'description': 'Ad-free music streaming with offline downloads',
            'category': 'Entertainment'
        },
        {
            'name': 'Netflix Premium (1 Month)',
            'price': 500,
            'description': 'Premium Netflix subscription with 4K streaming',
            'category': 'Entertainment'
        },
        {
            'name': 'Adobe Creative Cloud (1 Month)',
            'price': 500,
            'description': 'Access to all Adobe creative applications',
            'category': 'Design Tools'
        }
    ]
    
    @classmethod
    def validate_config(cls):
        """Validate required configuration"""
        required_vars = ['BOT_TOKEN', 'MONGODB_URI']
        missing_vars = []
        
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        return True
