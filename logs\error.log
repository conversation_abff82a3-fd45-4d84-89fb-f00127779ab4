2025-07-01 05:11:52 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326912, 28), 'signature': {'hash': b'\x89\x80\xb6A2(\x10\xde#\x90i\xdab\xd5\xd1?\xa9\x86\xb8\xb7', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326912, 28)}
2025-07-01 05:11:53 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:48 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326968, 15), 'signature': {'hash': b'\xfcrZ\xc0\x8e\x7f\xedu\xe5\xd6hl\xb2\x19D\x14\xa99\x17\xed', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326968, 15)}
2025-07-01 05:12:49 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:14:42 - src.services.product_service - ERROR - initialize_default_products:404 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:24:52 - __main__ - ERROR - run:1290 - ❌ Bot error: Cannot close a running event loop
2025-07-01 06:28:56 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:57 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:59 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:29:01 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-02 09:27:46 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-06 23:59:20 - __main__ - ERROR - main:1205 - ❌ Bot error: Timed out
2025-07-07 00:20:28 - __main__ - ERROR - error_handler:1581 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:20:32 - __main__ - ERROR - error_handler:1581 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:27:07 - __main__ - ERROR - balance_command:413 - Error in balance command: 'int' object has no attribute 'strftime'
2025-07-07 00:52:47 - __main__ - ERROR - error_handler:1612 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:52:51 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:04:38 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:05:46 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:17:54 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:17:58 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:17:59 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:04 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:06 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:11 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:14 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:17 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:21 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:46 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:50 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:51 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:56 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:58 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:19:03 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:20:17 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:30:45 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:49 - __main__ - ERROR - error_handler:1976 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:50 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:55 - __main__ - ERROR - error_handler:1976 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:56 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:45:57 - __main__ - ERROR - _show_bot_settings_menu:1756 - Error showing bot settings menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:45:57 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:01 - __main__ - ERROR - _show_bot_settings_menu:1756 - Error showing bot settings menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:01 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:02 - __main__ - ERROR - _show_broadcast_menu:1994 - Error showing broadcast menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:02 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:03 - __main__ - ERROR - _show_task_management_menu:1945 - Error showing task management menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:03 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:04 - __main__ - ERROR - _show_analytics_menu:1804 - Error showing analytics menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:04 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:05 - __main__ - ERROR - _show_system_health:1847 - Error showing system health: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:05 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:06 - __main__ - ERROR - _show_admin_logs:1905 - Error showing admin logs: object of type 'int' has no len()
2025-07-07 13:46:06 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 14:07:27 - final_bot - ERROR - _show_task_management_menu:2138 - Error showing task management menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_broadcast_menu:2198 - Error showing broadcast menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_analytics_menu:1957 - Error showing analytics menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_system_health:2011 - Error showing system health: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_admin_logs:2087 - Error showing admin logs: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_analytics_menu:1957 - Error showing analytics menu: object Mock can't be used in 'await' expression
2025-07-07 14:08:13 - final_bot - ERROR - _show_broadcast_menu:2198 - Error showing broadcast menu: object Mock can't be used in 'await' expression
2025-07-09 13:23:48 - __main__ - ERROR - _show_task_list:2723 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:48 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:51 - __main__ - ERROR - _show_task_list:2723 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:51 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:42:39 - __main__ - ERROR - main:414 - ❌ Bot error: 'NoneType' object has no attribute 'admin_panel_command'
2025-07-09 14:01:52 - __main__ - ERROR - main:432 - ❌ Bot error: 'NoneType' object has no attribute 'admin_panel_command'
2025-07-09 14:03:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:39 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:51 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:54 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:57 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:06 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:12 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:27 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - balance_command:323 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 14:05:20 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:23 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - help_command:287 - Error in help command: type object 'Config' has no attribute 'SUPPORT_USERNAME'
2025-07-09 14:05:25 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:25 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:06:24 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:06:29 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:07:15 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:15 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:15 - src.handlers.user_handlers - ERROR - handle_daily_bonus:129 - Error in daily bonus: 'UserService' object has no attribute 'can_claim_daily_bonus'
2025-07-09 14:07:18 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:18 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:18 - __main__ - ERROR - balance_command:322 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 14:07:20 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:20 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:21 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:21 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:21 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:08:14 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:08:14 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:08:14 - __main__ - ERROR - balance_command:322 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 14:10:45 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:10:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:10:51 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:10:55 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:10:57 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:02 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:05 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:09 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:12 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:18 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:23 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:32 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:40 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:52 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:56 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:12:14 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:12:18 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:12:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:12:48 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:13:19 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:13:23 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:13:54 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:13:58 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:14:28 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:14:33 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:15:04 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:15:08 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:15:39 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:15:43 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:16:14 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:16:18 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:16:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:16:53 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:17:23 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:17:28 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:17:58 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:18:03 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:18:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:18:38 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:19:08 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:19:12 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:19:43 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:19:47 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:20:18 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:20:22 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:20:53 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:20:57 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:21:28 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:21:32 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:22:03 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:22:07 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:22:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:22:42 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:23:12 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:23:17 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:23:47 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:23:51 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:24:22 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:24:26 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:24:57 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:25:01 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:25:32 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:25:36 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:26:06 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:26:11 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:26:42 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:26:46 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:27:16 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:27:21 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:27:51 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:27:55 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:28:26 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:28:30 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:29:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:29:05 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:29:36 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:29:40 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:30:10 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:30:14 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:30:45 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:30:49 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:31:20 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:31:24 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:31:55 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:31:59 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:32:29 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:32:34 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:33:04 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:33:08 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:33:39 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:33:43 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:34:14 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:34:18 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:34:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:34:53 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:35:23 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:35:28 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:35:58 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:36:02 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:36:33 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:36:37 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:37:08 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:37:12 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:37:42 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:37:47 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:38:17 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:38:21 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:38:52 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:38:56 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:39:27 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:39:31 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:40:02 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:40:06 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:40:36 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:40:41 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:41:12 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:41:16 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:41:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:41:51 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:42:21 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:42:25 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:42:56 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:43:00 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:43:31 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:43:35 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:44:06 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:44:10 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:44:40 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:44:45 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:45:15 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:45:19 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:45:50 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:45:54 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:46:25 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:46:29 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:47:00 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:47:04 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:47:35 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:47:39 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:48:09 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:48:14 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:48:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:48:48 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:49:19 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:49:23 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:49:54 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:49:58 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:50:29 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:50:33 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:51:03 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:51:08 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:51:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:51:42 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:52:13 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:52:17 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:52:48 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:52:52 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:53:23 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:53:27 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:53:57 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:54:02 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:54:32 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:54:36 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:55:07 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:55:12 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:55:42 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:55:46 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:56:17 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:56:21 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:56:52 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:56:56 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:57:27 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:57:31 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:58:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:58:06 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:58:36 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:58:40 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:59:11 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:59:15 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:59:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:59:50 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:00:21 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:00:25 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:00:55 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:01:00 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:01:30 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:01:34 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:02:05 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:02:09 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:02:40 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:02:44 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:03:15 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:03:19 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:03:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:03:54 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:04:24 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:04:28 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:04:59 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:05:03 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:05:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:05:38 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:06:09 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:06:13 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:06:43 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:06:47 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:07:18 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:07:22 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:07:53 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:07:57 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:08:28 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:08:32 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:09:03 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:09:07 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:09:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:09:43 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:10:13 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:10:17 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:10:48 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:10:52 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:11:23 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:11:27 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:11:58 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:12:02 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:12:32 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:12:37 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:13:07 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:13:11 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:13:42 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:13:46 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:14:17 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:14:21 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:14:52 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:14:56 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:15:27 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:15:30 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:31 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:15:45 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:45 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:45 - __main__ - ERROR - balance_command:323 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 15:15:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:47 - src.handlers.user_handlers - ERROR - handle_daily_bonus:129 - Error in daily bonus: 'UserService' object has no attribute 'can_claim_daily_bonus'
2025-07-09 15:15:48 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:48 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:49 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:49 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:49 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:49 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:52 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:52 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:52 - src.handlers.user_handlers - ERROR - handle_stats_menu:686 - Error in stats menu: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 15:15:53 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:53 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:53 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:53 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:53 - __main__ - ERROR - help_command:287 - Error in help command: type object 'Config' has no attribute 'SUPPORT_USERNAME'
2025-07-09 15:16:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:16:04 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:16:04 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:16:04 - src.handlers.user_handlers - ERROR - handle_daily_bonus:129 - Error in daily bonus: 'UserService' object has no attribute 'can_claim_daily_bonus'
2025-07-09 15:16:06 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:16:36 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:16:40 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:17:11 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:17:15 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:17:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:17:50 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:18:21 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:18:25 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:18:55 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:19:00 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:19:30 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:19:34 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:20:05 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:20:09 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:20:40 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:20:44 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:21:15 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:21:19 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:21:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:21:54 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:22:24 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:22:28 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:22:59 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:23:03 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:23:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:23:38 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:24:09 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:24:13 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:24:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:24:48 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:25:18 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:25:23 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:25:53 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:25:57 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:26:28 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:26:32 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:27:03 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:27:07 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:27:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:27:42 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:37:36 - __main__ - ERROR - error_handler:376 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 15:37:37 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 15:37:38 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 15:37:39 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 16:12:09 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 16:12:24 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ReadError: 
2025-07-09 16:12:25 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 16:12:26 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 16:12:28 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-10 12:28:37 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 12:28:43 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 12:29:09 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 12:35:26 - __main__ - ERROR - _show_task_creation_form:2762 - Error showing task creation form: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 12:35:26 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 12:35:31 - __main__ - ERROR - _show_task_creation_form:2762 - Error showing task creation form: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 12:35:31 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:08:16 - __main__ - ERROR - _show_task_creation_form:2920 - Error showing task creation form: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:08:16 - __main__ - ERROR - _handle_admin_callbacks:1615 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:08:23 - __main__ - ERROR - _show_task_list:2888 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:08:23 - __main__ - ERROR - _handle_admin_callbacks:1615 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:45:59 - __main__ - ERROR - _show_task_creation_form:2936 - Error showing task creation form: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:45:59 - __main__ - ERROR - _handle_admin_callbacks:1631 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:28 - __main__ - ERROR - _show_task_list:2904 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:28 - __main__ - ERROR - _handle_admin_callbacks:1631 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:33 - __main__ - ERROR - _show_task_statistics:4019 - Error showing task statistics: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:33 - __main__ - ERROR - _handle_admin_callbacks:1631 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:40 - __main__ - ERROR - _show_task_management_menu:2642 - Error showing task management menu: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-10 13:54:58 - final_bot - ERROR - _show_task_list:2930 - Error showing task list: 'async for' received an object from __aiter__ that does not implement __anext__: coroutine
2025-07-10 14:23:48 - final_bot - ERROR - _handle_referral_menu_callback:1950 - Error in referral menu callback: object int can't be used in 'await' expression
2025-07-10 14:48:39 - final_bot - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: FinalBotApp._verify_channel_membership() missing 1 required positional argument: 'task_id'
2025-07-10 14:48:40 - final_bot - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: FinalBotApp._verify_channel_membership() missing 1 required positional argument: 'task_id'
2025-07-10 14:48:40 - final_bot - ERROR - start_command:202 - Error in start command: FinalBotApp._verify_channel_membership() missing 1 required positional argument: 'task_id'
2025-07-10 14:58:09 - __main__ - ERROR - _verify_required_channels:222 - Error checking membership for user 8153676253 in channel -1001002414699235: Chat not found
2025-07-10 14:58:18 - __main__ - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: There is no text in the message to edit
2025-07-10 15:00:39 - __main__ - ERROR - _verify_required_channels:222 - Error checking membership for user 8153676253 in channel -1001002414699235: Chat not found
2025-07-10 15:00:45 - __main__ - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: There is no text in the message to edit
2025-07-10 15:01:31 - __main__ - ERROR - _verify_required_channels:222 - Error checking membership for user 8153676253 in channel -1001002414699235: Chat not found
2025-07-10 15:01:58 - __main__ - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: There is no text in the message to edit
2025-07-10 15:05:45 - final_bot - ERROR - _verify_required_channels:237 - Channel -1001296547211 not found - bot may not be admin or channel ID incorrect
2025-07-10 15:05:45 - final_bot - ERROR - _verify_required_channels:238 - Please verify: 1) Channel ID -1001296547211 is correct, 2) Bot is admin in channel
2025-07-10 15:05:47 - final_bot - ERROR - _animated_welcome_sequence:370 - Failed to create user: 'user'
2025-07-10 15:05:51 - final_bot - ERROR - _animated_welcome_sequence:370 - Failed to create user: 'user'
2025-07-10 15:05:51 - final_bot - ERROR - _handle_channel_verification_callback:2324 - Error editing verification message: Edit failed
2025-07-10 15:05:54 - final_bot - ERROR - _animated_welcome_sequence:370 - Failed to create user: 'user'
2025-07-10 15:09:22 - __main__ - ERROR - _verify_required_channels:237 - Channel -1001002414699235 not found - bot may not be admin or channel ID incorrect
2025-07-10 15:09:22 - __main__ - ERROR - _verify_required_channels:238 - Please verify: 1) Channel ID -1001002414699235 is correct, 2) Bot is admin in channel
2025-07-10 15:09:29 - __main__ - ERROR - _verify_required_channels:237 - Channel -1001002414699235 not found - bot may not be admin or channel ID incorrect
2025-07-10 15:09:29 - __main__ - ERROR - _verify_required_channels:238 - Please verify: 1) Channel ID -1001002414699235 is correct, 2) Bot is admin in channel
