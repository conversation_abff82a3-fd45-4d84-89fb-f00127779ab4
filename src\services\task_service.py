"""
Comprehensive Task Management Service
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from datetime import datetime, timezone
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.task import Task, UserTask, TaskSubmission, TaskType, UserTaskStatus, VerificationMode
from ..models.transaction import Transaction, TransactionType, TransactionStatus

logger = logging.getLogger(__name__)

class TaskService:
    """Service for comprehensive task management functionality"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.tasks_collection = db.tasks
        self.user_tasks_collection = db.user_tasks
        self.task_submissions_collection = db.task_submissions
        self.users_collection = db.users
        self.transactions_collection = db.transactions
    
    # ==================== TASK MANAGEMENT ====================
    
    async def create_task(self, admin_id: int, task_name: str, task_type: TaskType, 
                         reward_amount: float, **kwargs) -> Optional[Task]:
        """Create a new task"""
        try:
            task_id = f"task_{uuid.uuid4().hex[:12]}"
            
            task = Task(
                task_id=task_id,
                task_name=task_name,
                task_type=task_type,
                reward_amount=reward_amount,
                created_by=admin_id,
                **kwargs
            )
            
            await self.tasks_collection.insert_one(task.to_dict())
            
            logger.info(f"Task created: {task_id} by admin {admin_id}")
            return task
            
        except Exception as e:
            logger.error(f"Failed to create task: {e}")
            return None
    
    async def get_task(self, task_id: str) -> Optional[Task]:
        """Get task by ID"""
        try:
            task_data = await self.tasks_collection.find_one({'task_id': task_id})
            if task_data:
                task_data.pop('_id', None)
                return Task.from_dict(task_data)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get task {task_id}: {e}")
            return None
    
    async def get_active_tasks(self) -> List[Task]:
        """Get all active tasks"""
        try:
            cursor = self.tasks_collection.find({'is_active': True}).sort('created_at', -1)
            tasks = []
            
            async for task_data in cursor:
                task_data.pop('_id', None)
                tasks.append(Task.from_dict(task_data))
            
            return tasks
            
        except Exception as e:
            logger.error(f"Failed to get active tasks: {e}")
            return []
    
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """Update task"""
        try:
            updates['updated_at'] = datetime.now(timezone.utc).isoformat()
            
            result = await self.tasks_collection.update_one(
                {'task_id': task_id},
                {'$set': updates}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Failed to update task {task_id}: {e}")
            return False
    
    async def delete_task(self, task_id: str) -> bool:
        """Soft delete task"""
        try:
            return await self.update_task(task_id, {'is_active': False})
            
        except Exception as e:
            logger.error(f"Failed to delete task {task_id}: {e}")
            return False
    
    # ==================== USER TASK TRACKING ====================
    
    async def get_user_task(self, user_id: int, task_id: str) -> Optional[UserTask]:
        """Get user task status"""
        try:
            user_task_data = await self.user_tasks_collection.find_one({
                'user_id': user_id,
                'task_id': task_id
            })
            
            if user_task_data:
                user_task_data.pop('_id', None)
                return UserTask.from_dict(user_task_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get user task {user_id}/{task_id}: {e}")
            return None
    
    async def get_user_tasks(self, user_id: int) -> List[Tuple[Task, UserTask]]:
        """Get all tasks for a user with their completion status"""
        try:
            # Get all active tasks
            active_tasks = await self.get_active_tasks()
            
            # Get user's task statuses
            user_tasks_cursor = self.user_tasks_collection.find({'user_id': user_id})
            user_tasks_dict = {}
            
            async for user_task_data in user_tasks_cursor:
                user_task_data.pop('_id', None)
                user_task = UserTask.from_dict(user_task_data)
                user_tasks_dict[user_task.task_id] = user_task
            
            # Combine tasks with user status
            result = []
            for task in active_tasks:
                user_task = user_tasks_dict.get(task.task_id)
                if not user_task:
                    # Create default user task
                    user_task = UserTask(user_id=user_id, task_id=task.task_id)
                
                result.append((task, user_task))
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get user tasks for {user_id}: {e}")
            return []
    
    async def start_user_task(self, user_id: int, task_id: str) -> bool:
        """Start a task for user"""
        try:
            user_task = await self.get_user_task(user_id, task_id)
            
            if not user_task:
                user_task = UserTask(user_id=user_id, task_id=task_id)
            
            user_task.start_task()
            
            await self.user_tasks_collection.replace_one(
                {'user_id': user_id, 'task_id': task_id},
                user_task.to_dict(),
                upsert=True
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start task {task_id} for user {user_id}: {e}")
            return False
    
    async def submit_task_for_review(self, user_id: int, task_id: str, 
                                   submission_data: Dict[str, Any] = None,
                                   image_url: str = None, notes: str = None) -> bool:
        """Submit task for review"""
        try:
            user_task = await self.get_user_task(user_id, task_id)
            
            if not user_task:
                user_task = UserTask(user_id=user_id, task_id=task_id)
            
            user_task.submit_for_review(submission_data, image_url, notes)
            
            await self.user_tasks_collection.replace_one(
                {'user_id': user_id, 'task_id': task_id},
                user_task.to_dict(),
                upsert=True
            )
            
            # Create submission record
            submission_id = f"sub_{uuid.uuid4().hex[:12]}"
            submission = TaskSubmission(
                submission_id=submission_id,
                user_id=user_id,
                task_id=task_id,
                submission_type="image" if image_url else "verification",
                content_data=submission_data or {},
                image_urls=[image_url] if image_url else []
            )
            
            await self.task_submissions_collection.insert_one(submission.to_dict())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to submit task {task_id} for user {user_id}: {e}")
            return False
    
    async def complete_user_task(self, user_id: int, task_id: str, 
                               reviewed_by: int = None) -> bool:
        """Complete a user task and award reward"""
        try:
            # Get task details
            task = await self.get_task(task_id)
            if not task:
                return False
            
            # Update user task status
            user_task = await self.get_user_task(user_id, task_id)
            if not user_task:
                user_task = UserTask(user_id=user_id, task_id=task_id)
            
            user_task.complete_task(reviewed_by)
            
            await self.user_tasks_collection.replace_one(
                {'user_id': user_id, 'task_id': task_id},
                user_task.to_dict(),
                upsert=True
            )
            
            # Award reward to user
            await self._award_task_reward(user_id, task.reward_amount, task_id)
            
            # Update task completion count
            await self.tasks_collection.update_one(
                {'task_id': task_id},
                {'$inc': {'completion_count': 1}}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete task {task_id} for user {user_id}: {e}")
            return False
    
    async def reject_user_task(self, user_id: int, task_id: str, 
                             reviewed_by: int, reason: str, notes: str = None) -> bool:
        """Reject a user task submission"""
        try:
            user_task = await self.get_user_task(user_id, task_id)
            if not user_task:
                return False
            
            user_task.reject_task(reviewed_by, reason, notes)
            
            await self.user_tasks_collection.replace_one(
                {'user_id': user_id, 'task_id': task_id},
                user_task.to_dict(),
                upsert=True
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to reject task {task_id} for user {user_id}: {e}")
            return False
    
    # ==================== CHANNEL VERIFICATION ====================
    
    async def verify_channel_membership(self, bot, user_id: int, channel_id: str) -> bool:
        """Verify if user is a member of the specified channel"""
        try:
            # Use Telegram Bot API to check membership
            member = await bot.get_chat_member(channel_id, user_id)
            
            # Check if user is a member (not left or kicked)
            return member.status in ['member', 'administrator', 'creator']
            
        except Exception as e:
            logger.error(f"Failed to verify channel membership for user {user_id} in {channel_id}: {e}")
            return False
    
    # ==================== ADMIN REVIEW SYSTEM ====================
    
    async def get_pending_submissions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get pending task submissions for admin review"""
        try:
            cursor = self.task_submissions_collection.find({
                'review_status': 'pending'
            }).sort('submitted_at', 1).limit(limit)
            
            submissions = []
            async for submission_data in cursor:
                submission_data.pop('_id', None)
                
                # Get task and user details
                task = await self.get_task(submission_data['task_id'])
                user_data = await self.users_collection.find_one({'user_id': submission_data['user_id']})
                
                submissions.append({
                    'submission': TaskSubmission.from_dict(submission_data),
                    'task': task,
                    'user': user_data
                })
            
            return submissions
            
        except Exception as e:
            logger.error(f"Failed to get pending submissions: {e}")
            return []
    
    async def approve_submission(self, submission_id: str, admin_id: int, 
                               notes: str = None) -> bool:
        """Approve a task submission"""
        try:
            # Get submission
            submission_data = await self.task_submissions_collection.find_one({
                'submission_id': submission_id
            })
            
            if not submission_data:
                return False
            
            submission = TaskSubmission.from_dict(submission_data)
            submission.approve(admin_id, notes)
            
            # Update submission
            await self.task_submissions_collection.update_one(
                {'submission_id': submission_id},
                {'$set': submission.to_dict()}
            )
            
            # Complete the user task
            await self.complete_user_task(
                submission.user_id, 
                submission.task_id, 
                admin_id
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to approve submission {submission_id}: {e}")
            return False
    
    async def reject_submission(self, submission_id: str, admin_id: int, 
                              notes: str) -> bool:
        """Reject a task submission"""
        try:
            # Get submission
            submission_data = await self.task_submissions_collection.find_one({
                'submission_id': submission_id
            })
            
            if not submission_data:
                return False
            
            submission = TaskSubmission.from_dict(submission_data)
            submission.reject(admin_id, notes)
            
            # Update submission
            await self.task_submissions_collection.update_one(
                {'submission_id': submission_id},
                {'$set': submission.to_dict()}
            )
            
            # Reject the user task
            await self.reject_user_task(
                submission.user_id,
                submission.task_id,
                admin_id,
                "Submission rejected",
                notes
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to reject submission {submission_id}: {e}")
            return False
    
    # ==================== UTILITY METHODS ====================
    
    async def _award_task_reward(self, user_id: int, amount: float, task_id: str):
        """Award reward to user for completing task"""
        try:
            # Update user balance
            await self.users_collection.update_one(
                {'user_id': user_id},
                {
                    '$inc': {
                        'balance': amount
                    }
                }
            )
            
            # Create transaction record
            transaction = Transaction(
                user_id=user_id,
                amount=amount,
                transaction_type=TransactionType.TASK_REWARD,
                status=TransactionStatus.COMPLETED,
                description=f"Task completion reward",
                reference_id=task_id
            )
            
            await self.transactions_collection.insert_one(transaction.to_dict())
            
        except Exception as e:
            logger.error(f"Failed to award task reward to user {user_id}: {e}")
    
    async def get_task_statistics(self) -> Dict[str, Any]:
        """Get comprehensive task statistics"""
        try:
            # Basic task counts
            total_tasks = await self.tasks_collection.count_documents({})
            active_tasks = await self.tasks_collection.count_documents({'is_active': True})
            
            # Completion statistics
            total_completions = await self.user_tasks_collection.count_documents({
                'status': UserTaskStatus.COMPLETED.value
            })
            
            pending_reviews = await self.task_submissions_collection.count_documents({
                'review_status': 'pending'
            })
            
            # Reward statistics
            pipeline = [
                {'$match': {'is_active': True}},
                {'$group': {
                    '_id': None,
                    'total_rewards': {'$sum': {'$multiply': ['$reward_amount', '$completion_count']}}
                }}
            ]
            
            reward_result = await self.tasks_collection.aggregate(pipeline).to_list(1)
            total_rewards = reward_result[0]['total_rewards'] if reward_result else 0
            
            return {
                'total_tasks': total_tasks,
                'active_tasks': active_tasks,
                'total_completions': total_completions,
                'pending_reviews': pending_reviews,
                'total_rewards_distributed': round(total_rewards, 2),
                'generated_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get task statistics: {e}")
            return {}
