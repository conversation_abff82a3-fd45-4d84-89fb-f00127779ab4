#!/usr/bin/env python3
"""
Test script for the comprehensive task management system
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List

from config import Config
from src.database import Database
from src.models.task import Task, UserTask, TaskSubmission, TaskType, UserTaskStatus, VerificationMode
from src.services.task_service import TaskService

# Setup logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TaskSystemTester:
    """Test class for task management system"""
    
    def __init__(self):
        self.database = None
        self.task_service = None
        self.test_tasks = []
        self.test_users = [12345, 67890]  # Test user IDs
    
    async def setup(self):
        """Setup test environment"""
        logger.info("Setting up test environment...")
        
        # Initialize database
        self.database = Database()
        await self.database.connect()
        logger.info("Database connected")
        
        # Initialize task service
        self.task_service = TaskService(self.database.db)
        logger.info("Task service initialized")
    
    async def cleanup(self):
        """Clean up test data"""
        logger.info("Cleaning up test data...")
        
        # Delete test tasks
        for task_id in self.test_tasks:
            await self.database.tasks.delete_one({'task_id': task_id})
        
        # Delete test user tasks
        for user_id in self.test_users:
            await self.database.user_tasks.delete_many({'user_id': user_id})
        
        # Delete test submissions
        await self.database.task_submissions.delete_many({'user_id': {'$in': self.test_users}})
        
        logger.info(f"Cleaned up {len(self.test_tasks)} tasks and user data")
    
    async def test_create_tasks(self):
        """Test task creation"""
        logger.info("Testing task creation...")
        
        # Create a join channel task
        join_task = await self.task_service.create_task(
            admin_id=Config.ADMIN_USER_IDS[0] if Config.ADMIN_USER_IDS else 999,
            task_name="Join Test Channel",
            task_type=TaskType.JOIN_CHANNEL,
            reward_amount=25.0,
            description="Join our test channel",
            channel_id="@test_channel",
            join_link="https://t.me/test_channel"
        )
        
        if join_task:
            self.test_tasks.append(join_task.task_id)
            logger.info(f"Created join channel task: {join_task.task_id}")
        else:
            logger.error("Failed to create join channel task")
        
        # Create an image submission task
        image_task = await self.task_service.create_task(
            admin_id=Config.ADMIN_USER_IDS[0] if Config.ADMIN_USER_IDS else 999,
            task_name="Submit Test Image",
            task_type=TaskType.SUBMIT_IMAGE,
            reward_amount=50.0,
            description="Submit a test screenshot",
            instructions="Take a screenshot of the test page",
            verification_mode=VerificationMode.MANUAL_REVIEW
        )
        
        if image_task:
            self.test_tasks.append(image_task.task_id)
            logger.info(f"Created image submission task: {image_task.task_id}")
        else:
            logger.error("Failed to create image submission task")
        
        return len(self.test_tasks) == 2
    
    async def test_user_task_flow(self):
        """Test user task flow"""
        logger.info("Testing user task flow...")
        
        if not self.test_tasks:
            logger.error("No test tasks available")
            return False
        
        task_id = self.test_tasks[0]
        user_id = self.test_users[0]
        
        # Start task
        started = await self.task_service.start_user_task(user_id, task_id)
        if not started:
            logger.error("Failed to start task")
            return False
        
        logger.info(f"Started task {task_id} for user {user_id}")
        
        # Get user task
        user_task = await self.task_service.get_user_task(user_id, task_id)
        if not user_task or user_task.status != UserTaskStatus.IN_PROGRESS:
            logger.error("Task not started correctly")
            return False
        
        # Complete task
        completed = await self.task_service.complete_user_task(user_id, task_id)
        if not completed:
            logger.error("Failed to complete task")
            return False
        
        logger.info(f"Completed task {task_id} for user {user_id}")
        
        # Verify completion
        user_task = await self.task_service.get_user_task(user_id, task_id)
        if not user_task or user_task.status != UserTaskStatus.COMPLETED:
            logger.error("Task not completed correctly")
            return False
        
        return True
    
    async def test_image_submission_flow(self):
        """Test image submission flow"""
        logger.info("Testing image submission flow...")
        
        if len(self.test_tasks) < 2:
            logger.error("No image task available")
            return False
        
        task_id = self.test_tasks[1]  # Image task
        user_id = self.test_users[1]
        
        # Start task
        started = await self.task_service.start_user_task(user_id, task_id)
        if not started:
            logger.error("Failed to start image task")
            return False
        
        logger.info(f"Started image task {task_id} for user {user_id}")
        
        # Submit for review
        submitted = await self.task_service.submit_task_for_review(
            user_id=user_id,
            task_id=task_id,
            image_url="test_image_file_id",
            notes="Test submission"
        )
        
        if not submitted:
            logger.error("Failed to submit image task")
            return False
        
        logger.info(f"Submitted image task {task_id} for user {user_id}")
        
        # Verify submission status
        user_task = await self.task_service.get_user_task(user_id, task_id)
        if not user_task or user_task.status != UserTaskStatus.PENDING_REVIEW:
            logger.error("Image task not submitted correctly")
            return False
        
        # Get pending submissions
        submissions = await self.task_service.get_pending_submissions()
        if not submissions:
            logger.error("No pending submissions found")
            return False
        
        submission_id = submissions[0]['submission'].submission_id
        logger.info(f"Found pending submission: {submission_id}")
        
        # Approve submission
        admin_id = Config.ADMIN_USER_IDS[0] if Config.ADMIN_USER_IDS else 999
        approved = await self.task_service.approve_submission(submission_id, admin_id)
        
        if not approved:
            logger.error("Failed to approve submission")
            return False
        
        logger.info(f"Approved submission {submission_id}")
        
        # Verify task completion
        user_task = await self.task_service.get_user_task(user_id, task_id)
        if not user_task or user_task.status != UserTaskStatus.COMPLETED:
            logger.error("Task not completed after approval")
            return False
        
        return True
    
    async def run_tests(self):
        """Run all tests"""
        try:
            await self.setup()
            
            # Run tests
            create_success = await self.test_create_tasks()
            logger.info(f"Task creation test: {'PASSED' if create_success else 'FAILED'}")
            
            if create_success:
                user_flow_success = await self.test_user_task_flow()
                logger.info(f"User task flow test: {'PASSED' if user_flow_success else 'FAILED'}")
                
                image_flow_success = await self.test_image_submission_flow()
                logger.info(f"Image submission flow test: {'PASSED' if image_flow_success else 'FAILED'}")
                
                # Get task statistics
                stats = await self.task_service.get_task_statistics()
                logger.info(f"Task statistics: {stats}")
            
            # Clean up
            await self.cleanup()
            
        except Exception as e:
            logger.error(f"Test error: {e}")
        finally:
            # Close database connection
            if self.database and self.database.client:
                self.database.client.close()
                logger.info("Database connection closed")

async def main():
    """Main function"""
    tester = TaskSystemTester()
    await tester.run_tests()

if __name__ == "__main__":
    asyncio.run(main())
