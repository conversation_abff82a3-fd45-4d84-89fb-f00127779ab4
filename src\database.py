"""
Database connection and management for the Telegram Referral Bot
"""

import asyncio
import logging
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure
from typing import Optional
from config import Config

logger = logging.getLogger(__name__)

class Database:
    """Database connection manager using MongoDB Atlas"""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.db = None
        self._connected = False
    
    async def connect(self) -> bool:
        """Connect to MongoDB Atlas"""
        try:
            self.client = AsyncIOMotorClient(Config.MONGODB_URI)
            self.db = self.client[Config.DATABASE_NAME]
            
            # Test connection
            await self.client.admin.command('ping')
            self._connected = True
            
            # Initialize collections and indexes
            await self._initialize_collections()
            
            logger.info("Successfully connected to MongoDB Atlas")
            return True
            
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            self._connected = False
            logger.info("Disconnected from MongoDB")
    
    async def _initialize_collections(self):
        """Initialize collections and create indexes"""
        try:
            # Users collection indexes
            await self.db.users.create_index("user_id", unique=True)
            await self.db.users.create_index("username")
            await self.db.users.create_index("referral_code", unique=True, sparse=True)  # sparse=True allows multiple null values
            await self.db.users.create_index("referred_by")
            await self.db.users.create_index("created_at")
            
            # Transactions collection indexes
            await self.db.transactions.create_index("user_id")
            await self.db.transactions.create_index("transaction_type")
            await self.db.transactions.create_index("created_at")
            await self.db.transactions.create_index([("user_id", 1), ("created_at", -1)])
            
            # Referrals collection indexes
            await self.db.referrals.create_index("referrer_id")
            await self.db.referrals.create_index("referred_id")
            await self.db.referrals.create_index("created_at")
            await self.db.referrals.create_index([("referrer_id", 1), ("created_at", -1)])
            
            # Withdrawals collection indexes
            await self.db.withdrawals.create_index("user_id")
            await self.db.withdrawals.create_index("status")
            await self.db.withdrawals.create_index("created_at")
            await self.db.withdrawals.create_index([("status", 1), ("created_at", -1)])
            
            # Channels collection indexes
            await self.db.channels.create_index("channel_id", unique=True)
            await self.db.channels.create_index("is_active")
            
            # Products collection indexes
            await self.db.products.create_index("name", unique=True)
            await self.db.products.create_index("category")
            await self.db.products.create_index("is_active")
            
            # Daily bonuses collection indexes
            await self.db.daily_bonuses.create_index("user_id")
            await self.db.daily_bonuses.create_index("claimed_at")
            await self.db.daily_bonuses.create_index([("user_id", 1), ("claimed_at", -1)])
            
            # Settings collection
            await self.db.settings.create_index("key", unique=True)

            # Task management collections indexes
            await self.db.tasks.create_index("task_id", unique=True)
            await self.db.tasks.create_index("task_type")
            await self.db.tasks.create_index("is_active")
            await self.db.tasks.create_index("created_by")
            await self.db.tasks.create_index("created_at")

            # User tasks collection indexes
            await self.db.user_tasks.create_index([("user_id", 1), ("task_id", 1)], unique=True)
            await self.db.user_tasks.create_index("user_id")
            await self.db.user_tasks.create_index("task_id")
            await self.db.user_tasks.create_index("status")
            await self.db.user_tasks.create_index("submitted_at")

            # Task submissions collection indexes
            await self.db.task_submissions.create_index("submission_id", unique=True)
            await self.db.task_submissions.create_index("user_id")
            await self.db.task_submissions.create_index("task_id")
            await self.db.task_submissions.create_index("review_status")
            await self.db.task_submissions.create_index("submitted_at")
            await self.db.task_submissions.create_index("reviewed_by")

            # Task audit logs collection indexes
            await self.db.task_audit_logs.create_index("user_id")
            await self.db.task_audit_logs.create_index("task_id")
            await self.db.task_audit_logs.create_index("action")
            await self.db.task_audit_logs.create_index("timestamp")
            await self.db.task_audit_logs.create_index([("user_id", 1), ("timestamp", -1)])

            logger.info("Database collections and indexes initialized")
            
        except Exception as e:
            logger.error(f"Error initializing collections: {e}")
    
    @property
    def is_connected(self) -> bool:
        """Check if database is connected"""
        return self._connected
    
    async def health_check(self) -> bool:
        """Perform database health check"""
        try:
            if self.client is None:
                return False
            
            await self.client.admin.command('ping')
            return True
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    # Collection accessors
    @property
    def users(self):
        """Users collection"""
        return self.db.users if self.db is not None else None

    @property
    def transactions(self):
        """Transactions collection"""
        return self.db.transactions if self.db is not None else None

    @property
    def referrals(self):
        """Referrals collection"""
        return self.db.referrals if self.db is not None else None

    @property
    def withdrawals(self):
        """Withdrawals collection"""
        return self.db.withdrawals if self.db is not None else None

    @property
    def channels(self):
        """Channels collection"""
        return self.db.channels if self.db is not None else None

    @property
    def products(self):
        """Products collection"""
        return self.db.products if self.db is not None else None

    @property
    def daily_bonuses(self):
        """Daily bonuses collection"""
        return self.db.daily_bonuses if self.db is not None else None

    @property
    def settings(self):
        """Settings collection"""
        return self.db.settings if self.db is not None else None

    @property
    def tasks(self):
        """Tasks collection"""
        return self.db.tasks if self.db is not None else None

    @property
    def user_tasks(self):
        """User tasks collection"""
        return self.db.user_tasks if self.db is not None else None

    @property
    def task_submissions(self):
        """Task submissions collection"""
        return self.db.task_submissions if self.db is not None else None

    @property
    def task_audit_logs(self):
        """Task audit logs collection"""
        return self.db.task_audit_logs if self.db is not None else None
