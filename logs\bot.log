2025-07-11 16:33:42 - __main__ - INFO - main:6100 - 👋 <PERSON><PERSON> stopped by user
2025-07-11 16:33:54 - __main__ - INFO - main:6049 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:33:54 - __main__ - INFO - main:6050 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:33:54 - __main__ - INFO - main:6054 - ✅ Configuration validated
2025-07-11 16:33:55 - __main__ - INFO - main:6078 - ✅ All handlers added successfully
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6083 - 🚀 Starting bot in long polling mode...
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6084 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6085 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6086 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 16:36:00 - __main__ - ERROR - _show_task_list:4518 - Error showing task list: Can't parse entities: can't find end of the entity starting at byte offset 132
2025-07-11 16:40:59 - __main__ - INFO - _show_task_creation_summary:4691 - Task creation summary data: {'step': 'reward', 'button_name': 'BOT OPENING', 'task_type': 'submit_image', 'reference_image_url': 'AgACAgUAAxkBAAIChmhw6ZD770sTm2WG4pNHM98jOYeQAAL6xjEbPh2JV066fQVu-U95AQADAgADeAADNgQ', 'instructions': 'HID\nfdsfAS', 'reward': 100.0}
2025-07-11 16:41:20 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:41:23 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:50:06 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:56:37 - __main__ - INFO - main:6183 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:56:37 - __main__ - INFO - main:6184 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:56:37 - __main__ - INFO - main:6188 - ✅ Configuration validated
2025-07-11 16:56:38 - __main__ - INFO - main:6212 - ✅ All handlers added successfully
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6217 - 🚀 Starting bot in long polling mode...
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6218 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6219 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6220 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 16:56:53 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:56:58 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:56:59 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:04 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:05 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:11 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:15 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:18 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:22 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:25 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:30 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:36 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:39 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:47 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:51 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:58:03 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:58:08 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
