2025-07-01 05:11:47 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:11:47 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:11:52 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326912, 28), 'signature': {'hash': b'\x89\x80\xb6A2(\x10\xde#\x90i\xdab\xd5\xd1?\xa9\x86\xb8\xb7', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326912, 28)}
2025-07-01 05:11:52 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:11:52 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:11:52 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:11:52 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:11:53 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:11:53 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:11:53 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:11:53 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:43 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:12:43 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:12:48 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326968, 15), 'signature': {'hash': b'\xfcrZ\xc0\x8e\x7f\xedu\xe5\xd6hl\xb2\x19D\x14\xa99\x17\xed', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326968, 15)}
2025-07-01 05:12:48 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:12:48 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:12:48 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:12:48 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:12:49 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:12:49 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:12:49 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:12:49 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:14:34 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:14:34 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:14:42 - src.database - INFO - _initialize_collections:97 - Database collections and indexes initialized
2025-07-01 05:14:42 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:14:42 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:14:42 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:14:42 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:14:42 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:14:42 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:14:42 - src.services.product_service - ERROR - initialize_default_products:404 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:140 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:140 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:141 - No webhook, domain, or SSL configuration required.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:141 - No webhook, domain, or SSL configuration required.
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:24:43 - __main__ - INFO - initialize:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:24:43 - __main__ - INFO - initialize:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:24:43 - __main__ - INFO - initialize:47 - ✅ Configuration validated
2025-07-01 05:24:51 - __main__ - INFO - initialize:52 - ✅ Database connected successfully
2025-07-01 05:24:51 - __main__ - INFO - initialize:63 - ✅ Services initialized
2025-07-01 05:24:51 - __main__ - INFO - initialize:68 - ✅ Default products initialized
2025-07-01 05:24:52 - __main__ - INFO - initialize:77 - ✅ All handlers added successfully
2025-07-01 05:24:52 - __main__ - INFO - run:1271 - 🚀 Starting bot in long polling mode...
2025-07-01 05:24:52 - __main__ - INFO - run:1272 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:24:52 - __main__ - INFO - run:1273 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:24:52 - __main__ - INFO - run:1274 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:24:52 - __main__ - ERROR - run:1290 - ❌ Bot error: Cannot close a running event loop
2025-07-01 05:27:17 - __main__ - INFO - main:905 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:27:17 - __main__ - INFO - main:906 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:27:17 - __main__ - INFO - main:910 - ✅ Configuration validated
2025-07-01 05:27:18 - __main__ - INFO - main:927 - ✅ All handlers added successfully
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:932 - 🚀 Starting bot in long polling mode...
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:933 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:934 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:935 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:34:07 - __main__ - INFO - main:1203 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:34:07 - __main__ - INFO - main:1204 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:34:07 - __main__ - INFO - main:1208 - ✅ Configuration validated
2025-07-01 05:34:08 - __main__ - INFO - main:1225 - ✅ All handlers added successfully
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1230 - 🚀 Starting bot in long polling mode...
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1231 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1232 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1233 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:44:52 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:44:52 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:44:52 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:44:53 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:49:57 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:49:57 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:49:57 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:49:58 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:55:18 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:55:18 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:55:18 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:55:19 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 06:28:56 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:57 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:59 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:29:01 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-02 09:27:46 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-06 23:20:14 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-06 23:20:14 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-06 23:20:22 - src.database - INFO - _initialize_collections:97 - Database collections and indexes initialized
2025-07-06 23:20:22 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-06 23:20:22 - src - INFO - main:35 - Database connected successfully
2025-07-06 23:20:22 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-06 23:20:22 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-06 23:59:06 - __main__ - INFO - main:1159 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-06 23:59:06 - __main__ - INFO - main:1160 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-06 23:59:06 - __main__ - INFO - main:1164 - ✅ Configuration validated
2025-07-06 23:59:07 - __main__ - INFO - main:1181 - ✅ All handlers added successfully
2025-07-06 23:59:07 - __main__ - INFO - initialize_async_components:43 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-06 23:59:07 - __main__ - INFO - initialize_async_components:44 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-06 23:59:07 - __main__ - INFO - initialize_async_components:48 - ✅ Configuration validated
2025-07-06 23:59:15 - __main__ - INFO - initialize_async_components:53 - ✅ Database connected successfully
2025-07-06 23:59:15 - __main__ - INFO - initialize_async_components:64 - ✅ Services initialized
2025-07-06 23:59:15 - __main__ - INFO - initialize_async_components:69 - ✅ Default products initialized
2025-07-06 23:59:15 - __main__ - INFO - init_and_run:1186 - 🚀 Starting bot in long polling mode...
2025-07-06 23:59:15 - __main__ - INFO - init_and_run:1187 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-06 23:59:15 - __main__ - INFO - init_and_run:1188 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-06 23:59:15 - __main__ - INFO - init_and_run:1189 - 🤖 Bot username: @pro_gifts_bot
2025-07-06 23:59:20 - __main__ - ERROR - main:1205 - ❌ Bot error: Timed out
2025-07-07 00:09:32 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:09:32 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:09:32 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:09:33 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:09:33 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:09:33 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:09:33 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:09:40 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:09:40 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:09:40 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:09:40 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:09:40 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:09:40 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:09:40 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:12:41 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:12:41 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:12:41 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:12:42 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:12:42 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:12:42 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:12:42 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:12:48 - __main__ - INFO - main:1647 - 👋 Bot stopped by user
2025-07-07 00:13:53 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:13:53 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:13:53 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:13:54 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:13:54 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:13:54 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:13:54 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:14:01 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:14:01 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:14:01 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:14:01 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:14:01 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:14:01 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:14:01 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:16:48 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:16:48 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:16:48 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:16:48 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:16:48 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:16:48 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:16:48 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:16:55 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:16:55 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:16:55 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:16:55 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:16:55 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:16:55 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:16:55 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:20:19 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:20:19 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:20:19 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:20:20 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:20:20 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:20:20 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:20:20 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:20:26 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:20:26 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:20:26 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:20:26 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:20:26 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:20:26 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:20:26 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:20:28 - __main__ - ERROR - error_handler:1581 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:20:32 - __main__ - ERROR - error_handler:1581 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:21:39 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:21:39 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:21:39 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:21:40 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:21:40 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:21:40 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:21:40 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:21:48 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:21:48 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:21:48 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:21:48 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:21:48 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:21:48 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:21:48 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:26:46 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:26:46 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:26:46 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:26:47 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:26:47 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:26:47 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:26:47 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:26:53 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:26:53 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:26:54 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:26:54 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:26:54 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:26:54 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:26:54 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:27:07 - __main__ - ERROR - balance_command:413 - Error in balance command: 'int' object has no attribute 'strftime'
2025-07-07 00:33:26 - __main__ - INFO - main:1629 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:33:26 - __main__ - INFO - main:1630 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:33:26 - __main__ - INFO - main:1634 - ✅ Configuration validated
2025-07-07 00:33:27 - __main__ - INFO - main:1656 - ✅ All handlers added successfully
2025-07-07 00:33:27 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:33:27 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:33:27 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:33:34 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:33:34 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:33:34 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:33:34 - __main__ - INFO - init_and_run:1661 - 🚀 Starting bot in long polling mode...
2025-07-07 00:33:34 - __main__ - INFO - init_and_run:1662 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:33:34 - __main__ - INFO - init_and_run:1663 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:33:34 - __main__ - INFO - init_and_run:1664 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:35:09 - __main__ - INFO - main:1629 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:35:09 - __main__ - INFO - main:1630 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:35:09 - __main__ - INFO - main:1634 - ✅ Configuration validated
2025-07-07 00:35:09 - __main__ - INFO - main:1656 - ✅ All handlers added successfully
2025-07-07 00:35:09 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:35:09 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:35:09 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:35:16 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:35:16 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:35:16 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:35:16 - __main__ - INFO - init_and_run:1661 - 🚀 Starting bot in long polling mode...
2025-07-07 00:35:16 - __main__ - INFO - init_and_run:1662 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:35:16 - __main__ - INFO - init_and_run:1663 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:35:16 - __main__ - INFO - init_and_run:1664 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:52:38 - __main__ - INFO - main:1519 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:52:38 - __main__ - INFO - main:1520 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:52:38 - __main__ - INFO - main:1524 - ✅ Configuration validated
2025-07-07 00:52:38 - __main__ - INFO - main:1546 - ✅ All handlers added successfully
2025-07-07 00:52:38 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:52:38 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:52:38 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:52:45 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:52:45 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:52:45 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:52:45 - __main__ - INFO - init_and_run:1551 - 🚀 Starting bot in long polling mode...
2025-07-07 00:52:45 - __main__ - INFO - init_and_run:1552 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:52:45 - __main__ - INFO - init_and_run:1553 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:52:45 - __main__ - INFO - init_and_run:1554 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:52:47 - __main__ - ERROR - error_handler:1612 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:52:51 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:56:01 - __main__ - INFO - main:1519 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:56:01 - __main__ - INFO - main:1520 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:56:01 - __main__ - INFO - main:1524 - ✅ Configuration validated
2025-07-07 00:56:02 - __main__ - INFO - main:1546 - ✅ All handlers added successfully
2025-07-07 00:56:02 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:56:02 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:56:02 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:56:09 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:56:09 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:56:09 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:56:09 - __main__ - INFO - init_and_run:1551 - 🚀 Starting bot in long polling mode...
2025-07-07 00:56:09 - __main__ - INFO - init_and_run:1552 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:56:09 - __main__ - INFO - init_and_run:1553 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:56:09 - __main__ - INFO - init_and_run:1554 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 12:59:18 - __main__ - INFO - main:1519 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 12:59:18 - __main__ - INFO - main:1520 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 12:59:18 - __main__ - INFO - main:1524 - ✅ Configuration validated
2025-07-07 12:59:20 - __main__ - INFO - main:1546 - ✅ All handlers added successfully
2025-07-07 12:59:20 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 12:59:20 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 12:59:20 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 12:59:28 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 12:59:28 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 12:59:28 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 12:59:28 - __main__ - INFO - init_and_run:1551 - 🚀 Starting bot in long polling mode...
2025-07-07 12:59:28 - __main__ - INFO - init_and_run:1552 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 12:59:28 - __main__ - INFO - init_and_run:1553 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 12:59:28 - __main__ - INFO - init_and_run:1554 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:04:38 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:05:46 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:17:43 - __main__ - INFO - main:1436 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:17:43 - __main__ - INFO - main:1437 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:17:43 - __main__ - INFO - main:1441 - ✅ Configuration validated
2025-07-07 13:17:44 - __main__ - INFO - main:1463 - ✅ All handlers added successfully
2025-07-07 13:17:44 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:17:44 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:17:44 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 13:17:52 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 13:17:52 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 13:17:53 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 13:17:53 - __main__ - INFO - init_and_run:1468 - 🚀 Starting bot in long polling mode...
2025-07-07 13:17:53 - __main__ - INFO - init_and_run:1469 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:17:53 - __main__ - INFO - init_and_run:1470 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:17:53 - __main__ - INFO - init_and_run:1471 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:17:54 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:17:58 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:17:59 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:04 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:06 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:11 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:14 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:17 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:21 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:37 - __main__ - INFO - main:1436 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:18:37 - __main__ - INFO - main:1437 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:18:37 - __main__ - INFO - main:1441 - ✅ Configuration validated
2025-07-07 13:18:38 - __main__ - INFO - main:1463 - ✅ All handlers added successfully
2025-07-07 13:18:38 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:18:38 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:18:38 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 13:18:45 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 13:18:45 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 13:18:45 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 13:18:45 - __main__ - INFO - init_and_run:1468 - 🚀 Starting bot in long polling mode...
2025-07-07 13:18:45 - __main__ - INFO - init_and_run:1469 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:18:45 - __main__ - INFO - init_and_run:1470 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:18:45 - __main__ - INFO - init_and_run:1471 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:18:46 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:50 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:51 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:56 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:58 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:19:03 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:20:17 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:30:34 - __main__ - INFO - main:1993 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:30:34 - __main__ - INFO - main:1994 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:30:34 - __main__ - INFO - main:1998 - ✅ Configuration validated
2025-07-07 13:30:34 - __main__ - INFO - main:2021 - ✅ All handlers added successfully
2025-07-07 13:30:34 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:30:34 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:30:34 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:30:42 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:30:42 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:30:42 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:30:42 - __main__ - INFO - init_and_run:2026 - 🚀 Starting bot in long polling mode...
2025-07-07 13:30:42 - __main__ - INFO - init_and_run:2027 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:30:42 - __main__ - INFO - init_and_run:2028 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:30:42 - __main__ - INFO - init_and_run:2029 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:30:45 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:49 - __main__ - ERROR - error_handler:1976 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:50 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:55 - __main__ - ERROR - error_handler:1976 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:56 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:37:49 - __main__ - INFO - main:2828 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:37:49 - __main__ - INFO - main:2829 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:37:49 - __main__ - INFO - main:2833 - ✅ Configuration validated
2025-07-07 13:37:49 - __main__ - INFO - main:2856 - ✅ All handlers added successfully
2025-07-07 13:37:49 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:37:49 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:37:49 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:37:56 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:37:56 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:37:56 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:37:56 - __main__ - INFO - init_and_run:2861 - 🚀 Starting bot in long polling mode...
2025-07-07 13:37:56 - __main__ - INFO - init_and_run:2862 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:37:56 - __main__ - INFO - init_and_run:2863 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:37:56 - __main__ - INFO - init_and_run:2864 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:43:08 - __main__ - INFO - main:2856 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:43:08 - __main__ - INFO - main:2857 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:43:08 - __main__ - INFO - main:2861 - ✅ Configuration validated
2025-07-07 13:43:08 - __main__ - INFO - main:2884 - ✅ All handlers added successfully
2025-07-07 13:43:08 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:43:08 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:43:08 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:43:16 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:43:16 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:43:16 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:43:16 - __main__ - INFO - init_and_run:2889 - 🚀 Starting bot in long polling mode...
2025-07-07 13:43:16 - __main__ - INFO - init_and_run:2890 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:43:16 - __main__ - INFO - init_and_run:2891 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:43:16 - __main__ - INFO - init_and_run:2892 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:44:52 - __main__ - INFO - main:2856 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:44:52 - __main__ - INFO - main:2857 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:44:52 - __main__ - INFO - main:2861 - ✅ Configuration validated
2025-07-07 13:44:53 - __main__ - INFO - main:2884 - ✅ All handlers added successfully
2025-07-07 13:44:53 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:44:53 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:44:53 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:45:01 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:45:01 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:45:01 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:45:01 - __main__ - INFO - init_and_run:2889 - 🚀 Starting bot in long polling mode...
2025-07-07 13:45:01 - __main__ - INFO - init_and_run:2890 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:45:01 - __main__ - INFO - init_and_run:2891 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:45:01 - __main__ - INFO - init_and_run:2892 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:45:57 - __main__ - ERROR - _show_bot_settings_menu:1756 - Error showing bot settings menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:45:57 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:01 - __main__ - ERROR - _show_bot_settings_menu:1756 - Error showing bot settings menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:01 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:02 - __main__ - ERROR - _show_broadcast_menu:1994 - Error showing broadcast menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:02 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:03 - __main__ - ERROR - _show_task_management_menu:1945 - Error showing task management menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:03 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:04 - __main__ - ERROR - _show_analytics_menu:1804 - Error showing analytics menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:04 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:05 - __main__ - ERROR - _show_system_health:1847 - Error showing system health: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:05 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:06 - __main__ - ERROR - _show_admin_logs:1905 - Error showing admin logs: object of type 'int' has no len()
2025-07-07 13:46:06 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:48:42 - __main__ - INFO - main:2856 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:48:42 - __main__ - INFO - main:2857 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:48:42 - __main__ - INFO - main:2861 - ✅ Configuration validated
2025-07-07 13:48:42 - __main__ - INFO - main:2884 - ✅ All handlers added successfully
2025-07-07 13:48:42 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:48:42 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:48:42 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:48:50 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:48:50 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:48:50 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:48:50 - __main__ - INFO - init_and_run:2889 - 🚀 Starting bot in long polling mode...
2025-07-07 13:48:50 - __main__ - INFO - init_and_run:2890 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:48:50 - __main__ - INFO - init_and_run:2891 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:48:50 - __main__ - INFO - init_and_run:2892 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:56:33 - __main__ - INFO - main:3063 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:56:33 - __main__ - INFO - main:3064 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:56:33 - __main__ - INFO - main:3068 - ✅ Configuration validated
2025-07-07 13:56:33 - __main__ - INFO - main:3091 - ✅ All handlers added successfully
2025-07-07 13:56:33 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:56:33 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:56:33 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:56:41 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:56:41 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:56:41 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:56:41 - __main__ - INFO - init_and_run:3096 - 🚀 Starting bot in long polling mode...
2025-07-07 13:56:41 - __main__ - INFO - init_and_run:3097 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:56:41 - __main__ - INFO - init_and_run:3098 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:56:41 - __main__ - INFO - init_and_run:3099 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 14:04:26 - __main__ - INFO - main:3063 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 14:04:26 - __main__ - INFO - main:3064 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 14:04:26 - __main__ - INFO - main:3068 - ✅ Configuration validated
2025-07-07 14:04:26 - __main__ - INFO - main:3091 - ✅ All handlers added successfully
2025-07-07 14:04:26 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 14:04:26 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 14:04:26 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 14:04:33 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 14:04:33 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 14:04:33 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 14:04:33 - __main__ - INFO - init_and_run:3096 - 🚀 Starting bot in long polling mode...
2025-07-07 14:04:33 - __main__ - INFO - init_and_run:3097 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 14:04:33 - __main__ - INFO - init_and_run:3098 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 14:04:33 - __main__ - INFO - init_and_run:3099 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 14:07:27 - final_bot - ERROR - _show_task_management_menu:2138 - Error showing task management menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_broadcast_menu:2198 - Error showing broadcast menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_analytics_menu:1957 - Error showing analytics menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_system_health:2011 - Error showing system health: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_admin_logs:2087 - Error showing admin logs: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_analytics_menu:1957 - Error showing analytics menu: object Mock can't be used in 'await' expression
2025-07-07 14:08:13 - final_bot - ERROR - _show_broadcast_menu:2198 - Error showing broadcast menu: object Mock can't be used in 'await' expression
2025-07-07 14:15:44 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 14:15:44 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 14:15:44 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-07 14:15:45 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-07 14:15:45 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 14:15:45 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 14:15:45 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 14:15:52 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 14:15:52 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 14:15:52 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 14:15:52 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-07 14:15:52 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 14:15:52 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 14:15:52 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-09 12:45:10 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 12:45:10 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 12:45:10 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-09 12:45:10 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-09 12:45:10 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 12:45:10 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 12:45:10 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-09 12:45:21 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-09 12:45:21 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-09 12:45:21 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-09 12:45:21 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-09 12:45:21 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-09 12:45:21 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-09 12:45:21 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-09 12:46:26 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 12:46:26 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 12:46:26 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-09 12:46:27 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-09 12:46:27 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 12:46:27 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 12:46:27 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-09 12:46:38 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-09 12:46:38 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-09 12:46:39 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-09 12:46:39 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-09 12:46:39 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-09 12:46:39 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-09 12:46:39 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-09 13:12:28 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 13:12:28 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 13:12:28 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-09 13:12:29 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-09 13:12:29 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 13:12:29 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 13:12:29 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-09 13:12:40 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-09 13:12:40 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-09 13:12:41 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-09 13:12:41 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-09 13:12:41 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-09 13:12:41 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-09 13:12:41 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-09 13:23:48 - __main__ - ERROR - _show_task_list:2723 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:48 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:51 - __main__ - ERROR - _show_task_list:2723 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:51 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:42:38 - __main__ - INFO - main:371 - 🤖 Starting Telegram Referral Bot...
2025-07-09 13:42:38 - __main__ - INFO - main:375 - ✅ Configuration validated
2025-07-09 13:42:39 - __main__ - ERROR - main:414 - ❌ Bot error: 'NoneType' object has no attribute 'admin_panel_command'
2025-07-09 14:01:51 - __main__ - INFO - main:389 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:01:51 - __main__ - INFO - main:393 - ✅ Configuration validated
2025-07-09 14:01:52 - __main__ - ERROR - main:432 - ❌ Bot error: 'NoneType' object has no attribute 'admin_panel_command'
2025-07-09 14:02:39 - __main__ - INFO - main:389 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:02:39 - __main__ - INFO - main:393 - ✅ Configuration validated
2025-07-09 14:02:40 - __main__ - INFO - initialize_async_components:50 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:02:40 - __main__ - INFO - initialize_async_components:51 - 🔄 Mode: Long Polling
2025-07-09 14:02:40 - __main__ - INFO - initialize_async_components:55 - ✅ Configuration validated
2025-07-09 14:02:53 - __main__ - INFO - initialize_async_components:60 - ✅ Database connected
2025-07-09 14:02:54 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-09 14:02:54 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-09 14:02:54 - __main__ - INFO - init_and_run:416 - ✅ All handlers added
2025-07-09 14:02:54 - __main__ - INFO - init_and_run:417 - 🚀 Bot is running! Press Ctrl+C to stop.
2025-07-09 14:03:19 - __main__ - INFO - main:389 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:03:19 - __main__ - INFO - main:393 - ✅ Configuration validated
2025-07-09 14:03:19 - __main__ - INFO - initialize_async_components:50 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:03:19 - __main__ - INFO - initialize_async_components:51 - 🔄 Mode: Long Polling
2025-07-09 14:03:19 - __main__ - INFO - initialize_async_components:55 - ✅ Configuration validated
2025-07-09 14:03:32 - __main__ - INFO - initialize_async_components:60 - ✅ Database connected
2025-07-09 14:03:32 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-09 14:03:33 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-09 14:03:33 - __main__ - INFO - init_and_run:416 - ✅ All handlers added
2025-07-09 14:03:33 - __main__ - INFO - init_and_run:417 - 🚀 Bot is running! Press Ctrl+C to stop.
2025-07-09 14:03:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:39 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:51 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:54 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:57 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:06 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:12 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:27 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - balance_command:323 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 14:05:20 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:23 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - help_command:287 - Error in help command: type object 'Config' has no attribute 'SUPPORT_USERNAME'
2025-07-09 14:05:25 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:25 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:06:09 - __main__ - INFO - main:382 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:06:09 - __main__ - INFO - main:386 - ✅ Configuration validated
2025-07-09 14:06:09 - __main__ - INFO - initialize_async_components:50 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:06:09 - __main__ - INFO - initialize_async_components:51 - 🔄 Mode: Long Polling
2025-07-09 14:06:09 - __main__ - INFO - initialize_async_components:55 - ✅ Configuration validated
2025-07-09 14:06:22 - __main__ - INFO - initialize_async_components:60 - ✅ Database connected
2025-07-09 14:06:23 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-09 14:06:23 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-09 14:06:23 - __main__ - INFO - init_and_run:409 - ✅ All handlers added
2025-07-09 14:06:23 - __main__ - INFO - init_and_run:410 - 🚀 Bot is running! Press Ctrl+C to stop.
2025-07-09 14:06:24 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:06:29 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:06:51 - __main__ - INFO - main:382 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:06:51 - __main__ - INFO - main:386 - ✅ Configuration validated
2025-07-09 14:06:51 - __main__ - INFO - initialize_async_components:50 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:06:51 - __main__ - INFO - initialize_async_components:51 - 🔄 Mode: Long Polling
2025-07-09 14:06:51 - __main__ - INFO - initialize_async_components:55 - ✅ Configuration validated
2025-07-09 14:07:04 - __main__ - INFO - initialize_async_components:60 - ✅ Database connected
2025-07-09 14:07:04 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-09 14:07:04 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-09 14:07:04 - __main__ - INFO - init_and_run:409 - ✅ All handlers added
2025-07-09 14:07:04 - __main__ - INFO - init_and_run:410 - 🚀 Bot is running! Press Ctrl+C to stop.
2025-07-09 14:07:15 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:15 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:15 - src.handlers.user_handlers - ERROR - handle_daily_bonus:129 - Error in daily bonus: 'UserService' object has no attribute 'can_claim_daily_bonus'
2025-07-09 14:07:18 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:18 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:18 - __main__ - ERROR - balance_command:322 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 14:07:20 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:20 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:21 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:21 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:07:21 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:08:14 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:08:14 - __main__ - ERROR - check_user_permissions:125 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:08:14 - __main__ - ERROR - balance_command:322 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 14:10:29 - __main__ - INFO - main:389 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:10:29 - __main__ - INFO - main:393 - ✅ Configuration validated
2025-07-09 14:10:30 - __main__ - INFO - initialize_async_components:50 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:10:30 - __main__ - INFO - initialize_async_components:51 - 🔄 Mode: Long Polling
2025-07-09 14:10:30 - __main__ - INFO - initialize_async_components:55 - ✅ Configuration validated
2025-07-09 14:10:43 - __main__ - INFO - initialize_async_components:60 - ✅ Database connected
2025-07-09 14:10:44 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-09 14:10:44 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-09 14:10:44 - __main__ - INFO - init_and_run:416 - ✅ All handlers added
2025-07-09 14:10:44 - __main__ - INFO - init_and_run:417 - 🚀 Bot is running! Press Ctrl+C to stop.
2025-07-09 14:10:45 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:10:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:10:51 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:10:55 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:10:57 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:02 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:05 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:09 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:12 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:18 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:23 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:32 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:40 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:52 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:11:56 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:12:14 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:12:18 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:12:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:12:48 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:13:19 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:13:23 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:13:54 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:13:58 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:14:28 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:14:33 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:15:04 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:15:08 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:15:39 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:15:43 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:16:14 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:16:18 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:16:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:16:53 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:17:23 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:17:28 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:17:58 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:18:03 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:18:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:18:38 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:19:08 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:19:12 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:19:43 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:19:47 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:20:18 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:20:22 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:20:53 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:20:57 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:21:28 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:21:32 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:22:03 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:22:07 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:22:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:22:42 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:23:12 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:23:17 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:23:47 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:23:51 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:24:22 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:24:26 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:24:57 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:25:01 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:25:32 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:25:36 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:26:06 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:26:11 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:26:42 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:26:46 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:27:16 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:27:21 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:27:51 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:27:55 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:28:26 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:28:30 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:29:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:29:05 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:29:36 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:29:40 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:30:10 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:30:14 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:30:45 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:30:49 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:31:20 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:31:24 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:31:55 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:31:59 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:32:29 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:32:34 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:33:04 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:33:08 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:33:39 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:33:43 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:34:14 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:34:18 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:34:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:34:53 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:35:23 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:35:28 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:35:58 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:36:02 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:36:33 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:36:37 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:37:08 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:37:12 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:37:42 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:37:47 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:38:17 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:38:21 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:38:52 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:38:56 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:39:27 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:39:31 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:40:02 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:40:06 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:40:36 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:40:41 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:41:12 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:41:16 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:41:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:41:51 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:42:21 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:42:25 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:42:56 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:43:00 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:43:31 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:43:35 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:44:06 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:44:10 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:44:40 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:44:45 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:45:15 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:45:19 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:45:50 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:45:54 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:46:25 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:46:29 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:47:00 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:47:04 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:47:35 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:47:39 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:48:09 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:48:14 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:48:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:48:48 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:49:19 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:49:23 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:49:54 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:49:58 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:50:29 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:50:33 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:51:03 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:51:08 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:51:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:51:42 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:52:13 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:52:17 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:52:48 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:52:52 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:53:23 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:53:27 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:53:57 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:54:02 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:54:32 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:54:36 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:55:07 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:55:12 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:55:42 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:55:46 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:56:17 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:56:21 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:56:52 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:56:56 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:57:27 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:57:31 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:58:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:58:06 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:58:36 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:58:40 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:59:11 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:59:15 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:59:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:59:50 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:00:21 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:00:25 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:00:55 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:01:00 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:01:30 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:01:34 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:02:05 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:02:09 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:02:40 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:02:44 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:03:15 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:03:19 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:03:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:03:54 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:04:24 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:04:28 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:04:59 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:05:03 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:05:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:05:38 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:06:09 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:06:13 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:06:43 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:06:47 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:07:18 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:07:22 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:07:53 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:07:57 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:08:28 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:08:32 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:09:03 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:09:07 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:09:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:09:43 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:10:13 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:10:17 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:10:48 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:10:52 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:11:23 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:11:27 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:11:58 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:12:02 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:12:32 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:12:37 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:13:07 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:13:11 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:13:42 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:13:46 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:14:17 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:14:21 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:14:52 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:14:56 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:15:27 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:15:30 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:31 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:15:45 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:45 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:45 - __main__ - ERROR - balance_command:323 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 15:15:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:47 - src.handlers.user_handlers - ERROR - handle_daily_bonus:129 - Error in daily bonus: 'UserService' object has no attribute 'can_claim_daily_bonus'
2025-07-09 15:15:48 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:48 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:49 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:49 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:49 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:49 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:52 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:52 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:52 - src.handlers.user_handlers - ERROR - handle_stats_menu:686 - Error in stats menu: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 15:15:53 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:53 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:53 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:53 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:15:53 - __main__ - ERROR - help_command:287 - Error in help command: type object 'Config' has no attribute 'SUPPORT_USERNAME'
2025-07-09 15:16:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:16:04 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:16:04 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 15:16:04 - src.handlers.user_handlers - ERROR - handle_daily_bonus:129 - Error in daily bonus: 'UserService' object has no attribute 'can_claim_daily_bonus'
2025-07-09 15:16:06 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:16:36 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:16:40 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:17:11 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:17:15 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:17:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:17:50 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:18:21 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:18:25 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:18:55 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:19:00 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:19:30 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:19:34 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:20:05 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:20:09 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:20:40 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:20:44 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:21:15 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:21:19 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:21:49 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:21:54 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:22:24 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:22:28 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:22:59 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:23:03 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:23:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:23:38 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:24:09 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:24:13 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:24:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:24:48 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:25:18 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:25:23 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:25:53 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:25:57 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:26:28 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:26:32 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:27:03 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:27:07 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:27:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:27:42 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 15:37:36 - __main__ - ERROR - error_handler:376 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 15:37:37 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 15:37:38 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 15:37:39 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 16:12:09 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 16:12:24 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ReadError: 
2025-07-09 16:12:25 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 16:12:26 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-09 16:12:28 - __main__ - ERROR - error_handler:383 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-10 12:28:37 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 12:28:43 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 12:29:09 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 12:31:36 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 12:31:36 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 12:31:36 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-10 12:31:37 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-10 12:31:37 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 12:31:37 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 12:31:37 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-10 12:31:49 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-10 12:31:49 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-10 12:31:49 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-10 12:31:49 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-10 12:31:49 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 12:31:49 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 12:31:49 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 12:35:26 - __main__ - ERROR - _show_task_creation_form:2762 - Error showing task creation form: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 12:35:26 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 12:35:31 - __main__ - ERROR - _show_task_creation_form:2762 - Error showing task creation form: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 12:35:31 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:07:14 - __main__ - INFO - main:4200 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 13:07:14 - __main__ - INFO - main:4201 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 13:07:14 - __main__ - INFO - main:4205 - ✅ Configuration validated
2025-07-10 13:07:15 - __main__ - INFO - main:4229 - ✅ All handlers added successfully
2025-07-10 13:07:15 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 13:07:15 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 13:07:15 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 13:07:28 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 13:07:28 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 13:07:28 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 13:07:28 - __main__ - INFO - init_and_run:4234 - 🚀 Starting bot in long polling mode...
2025-07-10 13:07:28 - __main__ - INFO - init_and_run:4235 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 13:07:28 - __main__ - INFO - init_and_run:4236 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 13:07:28 - __main__ - INFO - init_and_run:4237 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 13:08:16 - __main__ - ERROR - _show_task_creation_form:2920 - Error showing task creation form: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:08:16 - __main__ - ERROR - _handle_admin_callbacks:1615 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:08:23 - __main__ - ERROR - _show_task_list:2888 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:08:23 - __main__ - ERROR - _handle_admin_callbacks:1615 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:12:58 - __main__ - INFO - _process_referral:1774 - Processed referral: 8153676253 -> 1049516929
2025-07-10 13:45:00 - __main__ - INFO - main:4477 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 13:45:00 - __main__ - INFO - main:4478 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 13:45:00 - __main__ - INFO - main:4482 - ✅ Configuration validated
2025-07-10 13:45:01 - __main__ - INFO - main:4506 - ✅ All handlers added successfully
2025-07-10 13:45:01 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 13:45:01 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 13:45:01 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 13:45:14 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 13:45:14 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 13:45:14 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 13:45:14 - __main__ - INFO - init_and_run:4511 - 🚀 Starting bot in long polling mode...
2025-07-10 13:45:14 - __main__ - INFO - init_and_run:4512 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 13:45:14 - __main__ - INFO - init_and_run:4513 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 13:45:14 - __main__ - INFO - init_and_run:4514 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 13:45:59 - __main__ - ERROR - _show_task_creation_form:2936 - Error showing task creation form: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:45:59 - __main__ - ERROR - _handle_admin_callbacks:1631 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:28 - __main__ - ERROR - _show_task_list:2904 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:28 - __main__ - ERROR - _handle_admin_callbacks:1631 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:33 - __main__ - ERROR - _show_task_statistics:4019 - Error showing task statistics: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:33 - __main__ - ERROR - _handle_admin_callbacks:1631 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-10 13:46:40 - __main__ - ERROR - _show_task_management_menu:2642 - Error showing task management menu: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-10 13:53:32 - __main__ - INFO - main:4459 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 13:53:32 - __main__ - INFO - main:4460 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 13:53:32 - __main__ - INFO - main:4464 - ✅ Configuration validated
2025-07-10 13:53:32 - __main__ - INFO - main:4488 - ✅ All handlers added successfully
2025-07-10 13:53:32 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 13:53:32 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 13:53:32 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 13:53:46 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 13:53:46 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 13:53:46 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 13:53:46 - __main__ - INFO - init_and_run:4493 - 🚀 Starting bot in long polling mode...
2025-07-10 13:53:46 - __main__ - INFO - init_and_run:4494 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 13:53:46 - __main__ - INFO - init_and_run:4495 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 13:53:46 - __main__ - INFO - init_and_run:4496 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 13:54:58 - final_bot - ERROR - _show_task_list:2930 - Error showing task list: 'async for' received an object from __aiter__ that does not implement __anext__: coroutine
2025-07-10 14:01:17 - __main__ - INFO - main:4459 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:01:17 - __main__ - INFO - main:4460 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:01:17 - __main__ - INFO - main:4464 - ✅ Configuration validated
2025-07-10 14:01:17 - __main__ - INFO - main:4488 - ✅ All handlers added successfully
2025-07-10 14:01:17 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:01:17 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:01:17 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 14:01:30 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 14:01:30 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 14:01:30 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 14:01:30 - __main__ - INFO - init_and_run:4493 - 🚀 Starting bot in long polling mode...
2025-07-10 14:01:30 - __main__ - INFO - init_and_run:4494 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 14:01:30 - __main__ - INFO - init_and_run:4495 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 14:01:30 - __main__ - INFO - init_and_run:4496 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 14:03:17 - __main__ - INFO - main:4459 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:03:17 - __main__ - INFO - main:4460 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:03:17 - __main__ - INFO - main:4464 - ✅ Configuration validated
2025-07-10 14:03:18 - __main__ - INFO - main:4488 - ✅ All handlers added successfully
2025-07-10 14:03:18 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:03:18 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:03:18 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 14:03:31 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 14:03:31 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 14:03:31 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 14:03:31 - __main__ - INFO - init_and_run:4493 - 🚀 Starting bot in long polling mode...
2025-07-10 14:03:31 - __main__ - INFO - init_and_run:4494 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 14:03:31 - __main__ - INFO - init_and_run:4495 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 14:03:31 - __main__ - INFO - init_and_run:4496 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 14:15:18 - __main__ - INFO - main:4712 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:15:18 - __main__ - INFO - main:4713 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:15:18 - __main__ - INFO - main:4717 - ✅ Configuration validated
2025-07-10 14:15:18 - __main__ - INFO - main:4741 - ✅ All handlers added successfully
2025-07-10 14:15:19 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:15:19 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:15:19 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 14:15:31 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 14:15:31 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 14:15:32 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 14:15:32 - __main__ - INFO - init_and_run:4746 - 🚀 Starting bot in long polling mode...
2025-07-10 14:15:32 - __main__ - INFO - init_and_run:4747 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 14:15:32 - __main__ - INFO - init_and_run:4748 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 14:15:32 - __main__ - INFO - init_and_run:4749 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 14:23:48 - final_bot - ERROR - _handle_referral_menu_callback:1950 - Error in referral menu callback: object int can't be used in 'await' expression
2025-07-10 14:26:07 - __main__ - INFO - main:4763 - 👋 Bot stopped by user
2025-07-10 14:26:11 - __main__ - INFO - main:4950 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:26:11 - __main__ - INFO - main:4951 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:26:11 - __main__ - INFO - main:4955 - ✅ Configuration validated
2025-07-10 14:26:12 - __main__ - INFO - main:4979 - ✅ All handlers added successfully
2025-07-10 14:26:12 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:26:12 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:26:12 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 14:26:26 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 14:26:26 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 14:26:26 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 14:26:26 - __main__ - INFO - init_and_run:4984 - 🚀 Starting bot in long polling mode...
2025-07-10 14:26:26 - __main__ - INFO - init_and_run:4985 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 14:26:26 - __main__ - INFO - init_and_run:4986 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 14:26:26 - __main__ - INFO - init_and_run:4987 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 14:28:51 - __main__ - INFO - main:4966 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:28:51 - __main__ - INFO - main:4967 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:28:51 - __main__ - INFO - main:4971 - ✅ Configuration validated
2025-07-10 14:28:52 - __main__ - INFO - main:4995 - ✅ All handlers added successfully
2025-07-10 14:28:52 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:28:52 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:28:52 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 14:29:05 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 14:29:05 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 14:29:05 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 14:29:05 - __main__ - INFO - init_and_run:5000 - 🚀 Starting bot in long polling mode...
2025-07-10 14:29:05 - __main__ - INFO - init_and_run:5001 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 14:29:05 - __main__ - INFO - init_and_run:5002 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 14:29:05 - __main__ - INFO - init_and_run:5003 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 14:48:39 - final_bot - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: FinalBotApp._verify_channel_membership() missing 1 required positional argument: 'task_id'
2025-07-10 14:48:40 - final_bot - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: FinalBotApp._verify_channel_membership() missing 1 required positional argument: 'task_id'
2025-07-10 14:48:40 - final_bot - ERROR - start_command:202 - Error in start command: FinalBotApp._verify_channel_membership() missing 1 required positional argument: 'task_id'
2025-07-10 14:57:10 - __main__ - INFO - main:5148 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:57:10 - __main__ - INFO - main:5149 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:57:10 - __main__ - INFO - main:5153 - ✅ Configuration validated
2025-07-10 14:57:11 - __main__ - INFO - main:5177 - ✅ All handlers added successfully
2025-07-10 14:57:11 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 14:57:11 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 14:57:11 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 14:57:25 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 14:57:25 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 14:57:25 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 14:57:25 - __main__ - INFO - init_and_run:5182 - 🚀 Starting bot in long polling mode...
2025-07-10 14:57:25 - __main__ - INFO - init_and_run:5183 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 14:57:25 - __main__ - INFO - init_and_run:5184 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 14:57:25 - __main__ - INFO - init_and_run:5185 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 14:58:09 - __main__ - ERROR - _verify_required_channels:222 - Error checking membership for user 8153676253 in channel -1001002414699235: Chat not found
2025-07-10 14:58:18 - __main__ - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: There is no text in the message to edit
2025-07-10 15:00:39 - __main__ - ERROR - _verify_required_channels:222 - Error checking membership for user 8153676253 in channel -1001002414699235: Chat not found
2025-07-10 15:00:45 - __main__ - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: There is no text in the message to edit
2025-07-10 15:01:03 - __main__ - INFO - main:5148 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:01:03 - __main__ - INFO - main:5149 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:01:03 - __main__ - INFO - main:5153 - ✅ Configuration validated
2025-07-10 15:01:03 - __main__ - INFO - main:5177 - ✅ All handlers added successfully
2025-07-10 15:01:03 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:01:03 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:01:03 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 15:01:19 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 15:01:19 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 15:01:19 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 15:01:19 - __main__ - INFO - init_and_run:5182 - 🚀 Starting bot in long polling mode...
2025-07-10 15:01:19 - __main__ - INFO - init_and_run:5183 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 15:01:19 - __main__ - INFO - init_and_run:5184 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 15:01:19 - __main__ - INFO - init_and_run:5185 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 15:01:31 - __main__ - ERROR - _verify_required_channels:222 - Error checking membership for user 8153676253 in channel -1001002414699235: Chat not found
2025-07-10 15:01:58 - __main__ - ERROR - _handle_channel_verification_callback:2327 - Error in channel verification callback: There is no text in the message to edit
2025-07-10 15:04:29 - __main__ - INFO - main:5199 - 👋 Bot stopped by user
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: member
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1001002414699235
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001002414699235: member
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: left
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:224 - User 12345 is not a valid member of channel -1001296547211: left
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:45 - final_bot - ERROR - _verify_required_channels:237 - Channel -1001296547211 not found - bot may not be admin or channel ID incorrect
2025-07-10 15:05:45 - final_bot - ERROR - _verify_required_channels:238 - Please verify: 1) Channel ID -1001296547211 is correct, 2) Bot is admin in channel
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: administrator
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1001002414699235
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001002414699235: administrator
2025-07-10 15:05:45 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:05:45 - final_bot - INFO - _handle_channel_verification_callback:2303 - Verification callback - Message type: text=True, caption=False
2025-07-10 15:05:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:05:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: member
2025-07-10 15:05:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1001002414699235
2025-07-10 15:05:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001002414699235: member
2025-07-10 15:05:46 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:05:47 - final_bot - ERROR - _animated_welcome_sequence:370 - Failed to create user: 'user'
2025-07-10 15:05:48 - final_bot - INFO - _handle_channel_verification_callback:2303 - Verification callback - Message type: text=False, caption=False
2025-07-10 15:05:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:05:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: left
2025-07-10 15:05:49 - final_bot - INFO - _verify_required_channels:224 - User 12345 is not a valid member of channel -1001296547211: left
2025-07-10 15:05:49 - final_bot - INFO - _handle_channel_verification_callback:2303 - Verification callback - Message type: text=False, caption=True
2025-07-10 15:05:50 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:05:50 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:50 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: member
2025-07-10 15:05:50 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1001002414699235
2025-07-10 15:05:50 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001002414699235: member
2025-07-10 15:05:50 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:05:51 - final_bot - ERROR - _animated_welcome_sequence:370 - Failed to create user: 'user'
2025-07-10 15:05:51 - final_bot - INFO - _handle_channel_verification_callback:2303 - Verification callback - Message type: text=True, caption=False
2025-07-10 15:05:51 - final_bot - ERROR - _handle_channel_verification_callback:2324 - Error editing verification message: Edit failed
2025-07-10 15:05:52 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:05:52 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:52 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: member
2025-07-10 15:05:52 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1001002414699235
2025-07-10 15:05:52 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001002414699235: member
2025-07-10 15:05:52 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:05:54 - final_bot - ERROR - _animated_welcome_sequence:370 - Failed to create user: 'user'
2025-07-10 15:05:54 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 99999
2025-07-10 15:05:54 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:05:54 - final_bot - INFO - _verify_required_channels:220 - User 99999 status in channel -1001296547211: member
2025-07-10 15:05:54 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1001002414699235
2025-07-10 15:05:54 - final_bot - INFO - _verify_required_channels:220 - User 99999 status in channel -1001002414699235: member
2025-07-10 15:05:54 - final_bot - INFO - _verify_required_channels:250 - ✅ User 99999 verified as member of all required channels
2025-07-10 15:07:59 - __main__ - INFO - main:5231 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:07:59 - __main__ - INFO - main:5232 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:07:59 - __main__ - INFO - main:5236 - ✅ Configuration validated
2025-07-10 15:08:00 - __main__ - INFO - main:5260 - ✅ All handlers added successfully
2025-07-10 15:08:00 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:08:00 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:08:00 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 15:08:14 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 15:08:14 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 15:08:14 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 15:08:14 - __main__ - INFO - init_and_run:5265 - 🚀 Starting bot in long polling mode...
2025-07-10 15:08:14 - __main__ - INFO - init_and_run:5266 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 15:08:14 - __main__ - INFO - init_and_run:5267 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 15:08:14 - __main__ - INFO - init_and_run:5268 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 15:09:21 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-10 15:09:21 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:09:22 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-10 15:09:22 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1001002414699235
2025-07-10 15:09:22 - __main__ - ERROR - _verify_required_channels:237 - Channel -1001002414699235 not found - bot may not be admin or channel ID incorrect
2025-07-10 15:09:22 - __main__ - ERROR - _verify_required_channels:238 - Please verify: 1) Channel ID -1001002414699235 is correct, 2) Bot is admin in channel
2025-07-10 15:09:27 - __main__ - INFO - _handle_channel_verification_callback:2303 - Verification callback - Message type: text=False, caption=True
2025-07-10 15:09:29 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-10 15:09:29 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:09:29 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-10 15:09:29 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1001002414699235
2025-07-10 15:09:29 - __main__ - ERROR - _verify_required_channels:237 - Channel -1001002414699235 not found - bot may not be admin or channel ID incorrect
2025-07-10 15:09:29 - __main__ - ERROR - _verify_required_channels:238 - Please verify: 1) Channel ID -1001002414699235 is correct, 2) Bot is admin in channel
2025-07-10 15:16:14 - __main__ - INFO - main:5282 - 👋 Bot stopped by user
2025-07-10 15:17:20 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:17:20 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:17:20 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: member
2025-07-10 15:17:20 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:17:20 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1002414699235: member
2025-07-10 15:17:20 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:25:22 - __main__ - INFO - main:5287 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:25:22 - __main__ - INFO - main:5288 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:25:22 - __main__ - INFO - main:5292 - ✅ Configuration validated
2025-07-10 15:25:23 - __main__ - INFO - main:5316 - ✅ All handlers added successfully
2025-07-10 15:25:23 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:25:23 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:25:23 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 15:25:38 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 15:25:38 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 15:25:38 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 15:25:38 - __main__ - INFO - init_and_run:5321 - 🚀 Starting bot in long polling mode...
2025-07-10 15:25:38 - __main__ - INFO - init_and_run:5322 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 15:25:38 - __main__ - INFO - init_and_run:5323 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 15:25:38 - __main__ - INFO - init_and_run:5324 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 15:25:49 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-10 15:25:49 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:25:52 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-10 15:25:52 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:25:53 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-10 15:25:53 - __main__ - INFO - _verify_required_channels:250 - ✅ User 8153676253 verified as member of all required channels
2025-07-10 15:26:18 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-10 15:26:18 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:26:19 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: administrator
2025-07-10 15:26:19 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:26:19 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1002414699235: left
2025-07-10 15:26:19 - __main__ - INFO - _verify_required_channels:224 - User 1381431908 is not a valid member of channel -1002414699235: left
2025-07-10 15:26:41 - __main__ - INFO - _handle_channel_verification_callback:2308 - Verification callback - Message type: text=False, caption=True
2025-07-10 15:26:42 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-10 15:26:42 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:26:42 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: left
2025-07-10 15:26:42 - __main__ - INFO - _verify_required_channels:224 - User 1381431908 is not a valid member of channel -1001296547211: left
2025-07-10 15:26:55 - __main__ - INFO - _handle_channel_verification_callback:2308 - Verification callback - Message type: text=True, caption=False
2025-07-10 15:26:57 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-10 15:26:57 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:26:57 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: member
2025-07-10 15:26:57 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:26:57 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1002414699235: member
2025-07-10 15:26:57 - __main__ - INFO - _verify_required_channels:250 - ✅ User 1381431908 verified as member of all required channels
2025-07-10 15:28:42 - __main__ - INFO - main:5338 - 👋 Bot stopped by user
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: member
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1002414699235: member
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: administrator
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1002414699235: administrator
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: creator
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1002414699235: creator
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:250 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: left
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:224 - User 12345 is not a valid member of channel -1001296547211: left
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: kicked
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:224 - User 12345 is not a valid member of channel -1001296547211: kicked
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: restricted
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:224 - User 12345 is not a valid member of channel -1001296547211: restricted
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: unknown_status
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:229 - User 12345 has invalid status in channel -1001296547211: unknown_status
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: administrator
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1002414699235: member
2025-07-10 15:29:49 - final_bot - INFO - _verify_required_channels:250 - ✅ User 1381431908 verified as member of all required channels
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 12345 accepted - valid status in channel -1001296547211: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1002414699235: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 12345 accepted - valid status in channel -1002414699235: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:260 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 12345 accepted - valid status in channel -1001296547211: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1002414699235: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 12345 accepted - valid status in channel -1002414699235: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:260 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: creator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 12345 accepted - valid status in channel -1001296547211: creator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1002414699235: creator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 12345 accepted - valid status in channel -1002414699235: creator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:260 - ✅ User 12345 verified as member of all required channels
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: left
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:228 - ❌ User 12345 rejected - invalid status in channel -1001296547211: left
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:229 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: kicked
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:228 - ❌ User 12345 rejected - invalid status in channel -1001296547211: kicked
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:229 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: restricted
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:228 - ❌ User 12345 rejected - invalid status in channel -1001296547211: restricted
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:229 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 12345
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 12345 status in channel -1001296547211: unknown_status
2025-07-10 15:30:46 - final_bot - WARNING - _verify_required_channels:237 - ⚠️ User 12345 has unknown status in channel -1001296547211: unknown_status
2025-07-10 15:30:46 - final_bot - WARNING - _verify_required_channels:238 -    Expected statuses: ['member', 'administrator', 'creator']
2025-07-10 15:30:46 - final_bot - WARNING - _verify_required_channels:239 -    Treating unknown status as invalid for security
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1001296547211: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1002414699235: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1002414699235: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:260 - ✅ User 1381431908 verified as member of all required channels
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1001296547211: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1002414699235: creator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:260 - ✅ User 8153676253 verified as member of all required channels
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1001296547211: administrator
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1002414699235: left
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:228 - ❌ User 1381431908 rejected - invalid status in channel -1002414699235: left
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:229 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: left
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:228 - ❌ User 1381431908 rejected - invalid status in channel -1001296547211: left
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:229 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1001296547211: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1002414699235: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1002414699235: member
2025-07-10 15:30:46 - final_bot - INFO - _verify_required_channels:260 - ✅ User 1381431908 verified as member of all required channels
2025-07-10 15:32:28 - __main__ - INFO - main:5299 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:32:28 - __main__ - INFO - main:5300 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:32:28 - __main__ - INFO - main:5304 - ✅ Configuration validated
2025-07-10 15:32:29 - __main__ - INFO - main:5328 - ✅ All handlers added successfully
2025-07-10 15:32:29 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:32:29 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:32:29 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 15:32:43 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 15:32:43 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 15:32:43 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 15:32:43 - __main__ - INFO - init_and_run:5333 - 🚀 Starting bot in long polling mode...
2025-07-10 15:32:43 - __main__ - INFO - init_and_run:5334 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 15:32:43 - __main__ - INFO - init_and_run:5335 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 15:32:43 - __main__ - INFO - init_and_run:5336 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 15:32:58 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-10 15:32:58 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:32:59 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-10 15:32:59 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1001296547211: administrator
2025-07-10 15:32:59 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:32:59 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-10 15:32:59 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1002414699235: creator
2025-07-10 15:32:59 - __main__ - INFO - _verify_required_channels:260 - ✅ User 8153676253 verified as member of all required channels
2025-07-10 15:34:02 - __main__ - INFO - main:5299 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:34:02 - __main__ - INFO - main:5300 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:34:02 - __main__ - INFO - main:5304 - ✅ Configuration validated
2025-07-10 15:34:02 - __main__ - INFO - main:5328 - ✅ All handlers added successfully
2025-07-10 15:34:02 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:34:02 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:34:02 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 15:34:17 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 15:34:17 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 15:34:17 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 15:34:17 - __main__ - INFO - init_and_run:5333 - 🚀 Starting bot in long polling mode...
2025-07-10 15:34:17 - __main__ - INFO - init_and_run:5334 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 15:34:17 - __main__ - INFO - init_and_run:5335 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 15:34:17 - __main__ - INFO - init_and_run:5336 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 15:34:33 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-10 15:34:33 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:34:33 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-10 15:34:33 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1001296547211: administrator
2025-07-10 15:34:33 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:34:33 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-10 15:34:33 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1002414699235: creator
2025-07-10 15:34:33 - __main__ - INFO - _verify_required_channels:260 - ✅ User 8153676253 verified as member of all required channels
2025-07-10 15:35:37 - __main__ - INFO - main:5299 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:35:37 - __main__ - INFO - main:5300 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:35:37 - __main__ - INFO - main:5304 - ✅ Configuration validated
2025-07-10 15:35:38 - __main__ - INFO - main:5328 - ✅ All handlers added successfully
2025-07-10 15:35:38 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:35:38 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:35:38 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 15:35:51 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 15:35:51 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 15:35:51 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 15:35:51 - __main__ - INFO - init_and_run:5333 - 🚀 Starting bot in long polling mode...
2025-07-10 15:35:51 - __main__ - INFO - init_and_run:5334 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 15:35:51 - __main__ - INFO - init_and_run:5335 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 15:35:51 - __main__ - INFO - init_and_run:5336 - 🤖 Bot username: @pro_gifts_bot
2025-07-10 15:36:46 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-10 15:36:46 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-10 15:36:47 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-10 15:36:47 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1001296547211: administrator
2025-07-10 15:36:47 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-10 15:36:47 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-10 15:36:47 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1002414699235: creator
2025-07-10 15:36:47 - __main__ - INFO - _verify_required_channels:260 - ✅ User 8153676253 verified as member of all required channels
2025-07-10 15:49:12 - __main__ - INFO - main:5307 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:49:12 - __main__ - INFO - main:5308 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:49:12 - __main__ - INFO - main:5312 - ✅ Configuration validated
2025-07-10 15:49:12 - __main__ - INFO - main:5336 - ✅ All handlers added successfully
2025-07-10 15:49:12 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-10 15:49:12 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-10 15:49:12 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-10 15:49:26 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-10 15:49:26 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-10 15:49:26 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-10 15:49:26 - __main__ - INFO - init_and_run:5341 - 🚀 Starting bot in long polling mode...
2025-07-10 15:49:26 - __main__ - INFO - init_and_run:5342 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-10 15:49:26 - __main__ - INFO - init_and_run:5343 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-10 15:49:26 - __main__ - INFO - init_and_run:5344 - 🤖 Bot username: @pro_gifts_bot
